"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pms/manage_tickets/[id]/page",{

/***/ "(app-pages-browser)/./app/pms/manage_tickets/tickets.ts":
/*!*******************************************!*\
  !*** ./app/pms/manage_tickets/tickets.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bulkDeleteTickets: function() { return /* binding */ bulkDeleteTickets; },\n/* harmony export */   deleteTicket: function() { return /* binding */ deleteTicket; },\n/* harmony export */   fetchTags: function() { return /* binding */ fetchTags; },\n/* harmony export */   fetchTicket: function() { return /* binding */ fetchTicket; },\n/* harmony export */   fetchTickets: function() { return /* binding */ fetchTickets; },\n/* harmony export */   fetchUsers: function() { return /* binding */ fetchUsers; },\n/* harmony export */   updateTicket: function() { return /* binding */ updateTicket; },\n/* harmony export */   updateTicketStatus: function() { return /* binding */ updateTicketStatus; }\n/* harmony export */ });\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n\n// Fetch all tickets\nasync function fetchTickets() {\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.ticket_routes.GET_TICKETS, {\n        method: \"GET\"\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch tickets\");\n    const data = await res.json();\n    // Map API response to UI Ticket interface\n    return data.data.map((apiTicket)=>{\n        // Find the correct current stage object\n        let currentStage = undefined;\n        if (apiTicket.currentStageId && Array.isArray(apiTicket.stages)) {\n            currentStage = apiTicket.stages.find((stage)=>stage.pipelineStageId === apiTicket.currentStageId);\n        } else if (apiTicket.currentStage) {\n            currentStage = apiTicket.currentStage;\n        } else if (Array.isArray(apiTicket.stages) && apiTicket.stages.length > 0) {\n            currentStage = apiTicket.stages[apiTicket.stages.length - 1];\n        }\n        return {\n            id: apiTicket.id,\n            title: apiTicket.title || \"Ticket for Invoice \".concat(apiTicket.workItemId),\n            description: apiTicket.description || \"\",\n            status: \"\",\n            priority: (apiTicket.priority || \"low\").toLowerCase(),\n            dueDate: Array.isArray(apiTicket.stages) && apiTicket.stages.length > 0 ? apiTicket.stages[apiTicket.stages.length - 1].dueAt : null,\n            createdAt: apiTicket.createdAt ? new Date(apiTicket.createdAt) : new Date(),\n            updatedAt: apiTicket.updatedAt ? new Date(apiTicket.updatedAt) : new Date(),\n            tags: apiTicket.tags || [],\n            comments: apiTicket.comments || [],\n            pipeline: apiTicket.pipeline || {},\n            currentStage: currentStage,\n            stages: apiTicket.stages || [],\n            createdBy: apiTicket.createdBy || \"\",\n            owner: apiTicket.owner || \"\"\n        };\n    });\n}\n// Fetch all users\nasync function fetchUsers() {\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.employee_routes.GETALL_USERS, {\n        method: \"GET\",\n        credentials: \"include\"\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch users\");\n    const data = await res.json();\n    // Map id to string\n    return data.data.map((user)=>({\n            ...user,\n            id: String(user.id)\n        }));\n}\n// Fetch a single ticket by ID\nasync function fetchTicket(id) {\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.ticket_routes.GET_TICKET_BY_ID(id), {\n        method: \"GET\"\n    });\n    if (!res.ok) return null;\n    const data = await res.json();\n    const apiTicket = data.data;\n    // Map tags to UI format\n    const tags = (apiTicket.tags || []).map((tag)=>({\n            id: tag.id,\n            tagName: tag.tagName || tag.name,\n            name: tag.tagName || tag.name,\n            color: tag.color || \"bg-gray-100 text-gray-800\",\n            createdBy: tag.createdBy,\n            createdAt: tag.createdAt\n        }));\n    // Map dueDate from last stage if available\n    let dueDate = null;\n    if (Array.isArray(apiTicket.stages) && apiTicket.stages.length > 0) {\n        dueDate = apiTicket.stages[apiTicket.stages.length - 1].dueAt || null;\n    }\n    return {\n        id: apiTicket.id,\n        title: apiTicket.title || \"Ticket for Invoice \".concat(apiTicket.workItemId),\n        description: apiTicket.description || \"\",\n        status: apiTicket.status || \"todo\",\n        priority: (apiTicket.priority || \"low\").toLowerCase(),\n        dueDate: dueDate ? new Date(dueDate) : null,\n        createdAt: apiTicket.createdAt ? new Date(apiTicket.createdAt) : new Date(),\n        updatedAt: apiTicket.updatedAt ? new Date(apiTicket.updatedAt) : new Date(),\n        tags,\n        comments: apiTicket.comments || [],\n        pipeline: apiTicket.pipeline || {},\n        currentStage: apiTicket.currentStage || undefined,\n        currentStageId: apiTicket.currentStageId,\n        stages: apiTicket.stages || [],\n        createdBy: apiTicket.createdBy || \"\",\n        owner: apiTicket.owner || \"\"\n    };\n}\n// Update a ticket (status or other fields)\nasync function updateTicket(id, updates, username) {\n    const payload = {\n        ...updates,\n        ticketId: id,\n        ...username && {\n            updatedBy: username\n        }\n    };\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.ticket_routes.UPDATE_TICKET(id), {\n        method: \"PUT\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(payload)\n    });\n    if (!res.ok) throw new Error(\"Failed to update ticket\");\n    const data = await res.json();\n    console.log(\"Update ticket API response:\", data);\n    const updatedTicket = data.ticket || data.data;\n    if (!updatedTicket) throw new Error(\"No updated ticket returned from API\");\n    return updatedTicket;\n}\n// Update only the status of a ticket\nasync function updateTicketStatus(id, status, username) {\n    // The backend expects the full update body, so send only status if that's all that's changing\n    return updateTicket(id, {\n        status\n    }, username);\n}\n// Delete a ticket by ID\nasync function deleteTicket(id, username) {\n    const payload = username ? {\n        deletedBy: username\n    } : {};\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.ticket_routes.DELETE_TICKET(id), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(payload)\n    });\n    if (!res.ok) throw new Error(\"Failed to delete ticket\");\n}\n// Bulk delete tickets by looping over IDs\nasync function bulkDeleteTickets(ids, username) {\n    for (const id of ids){\n        await deleteTicket(id, username);\n    }\n}\n// Fetch all tags\nasync function fetchTags() {\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.tag_routes.GET_ALL_TAGS, {\n        method: \"GET\"\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch tags\");\n    const data = await res.json();\n    return data.data;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pms/manage_tickets/tickets.ts\n"));

/***/ })

});