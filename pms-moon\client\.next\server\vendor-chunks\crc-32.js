/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/crc-32";
exports.ids = ["vendor-chunks/crc-32"];
exports.modules = {

/***/ "(ssr)/./node_modules/crc-32/crc32.js":
/*!**************************************!*\
  !*** ./node_modules/crc-32/crc32.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*! crc32.js (C) 2014-present SheetJS -- http://sheetjs.com */\n/* vim: set ts=2: */\n/*exported CRC32 */\nvar CRC32;\n(function (factory) {\n\t/*jshint ignore:start */\n\t/*eslint-disable */\n\tif(typeof DO_NOT_EXPORT_CRC === 'undefined') {\n\t\tif(true) {\n\t\t\tfactory(exports);\n\t\t} else {}\n\t} else {\n\t\tfactory(CRC32 = {});\n\t}\n\t/*eslint-enable */\n\t/*jshint ignore:end */\n}(function(CRC32) {\nCRC32.version = '1.2.2';\n/*global Int32Array */\nfunction signed_crc_table() {\n\tvar c = 0, table = new Array(256);\n\n\tfor(var n =0; n != 256; ++n){\n\t\tc = n;\n\t\tc = ((c&1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1));\n\t\tc = ((c&1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1));\n\t\tc = ((c&1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1));\n\t\tc = ((c&1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1));\n\t\tc = ((c&1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1));\n\t\tc = ((c&1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1));\n\t\tc = ((c&1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1));\n\t\tc = ((c&1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1));\n\t\ttable[n] = c;\n\t}\n\n\treturn typeof Int32Array !== 'undefined' ? new Int32Array(table) : table;\n}\n\nvar T0 = signed_crc_table();\nfunction slice_by_16_tables(T) {\n\tvar c = 0, v = 0, n = 0, table = typeof Int32Array !== 'undefined' ? new Int32Array(4096) : new Array(4096) ;\n\n\tfor(n = 0; n != 256; ++n) table[n] = T[n];\n\tfor(n = 0; n != 256; ++n) {\n\t\tv = T[n];\n\t\tfor(c = 256 + n; c < 4096; c += 256) v = table[c] = (v >>> 8) ^ T[v & 0xFF];\n\t}\n\tvar out = [];\n\tfor(n = 1; n != 16; ++n) out[n - 1] = typeof Int32Array !== 'undefined' ? table.subarray(n * 256, n * 256 + 256) : table.slice(n * 256, n * 256 + 256);\n\treturn out;\n}\nvar TT = slice_by_16_tables(T0);\nvar T1 = TT[0],  T2 = TT[1],  T3 = TT[2],  T4 = TT[3],  T5 = TT[4];\nvar T6 = TT[5],  T7 = TT[6],  T8 = TT[7],  T9 = TT[8],  Ta = TT[9];\nvar Tb = TT[10], Tc = TT[11], Td = TT[12], Te = TT[13], Tf = TT[14];\nfunction crc32_bstr(bstr, seed) {\n\tvar C = seed ^ -1;\n\tfor(var i = 0, L = bstr.length; i < L;) C = (C>>>8) ^ T0[(C^bstr.charCodeAt(i++))&0xFF];\n\treturn ~C;\n}\n\nfunction crc32_buf(B, seed) {\n\tvar C = seed ^ -1, L = B.length - 15, i = 0;\n\tfor(; i < L;) C =\n\t\tTf[B[i++] ^ (C & 255)] ^\n\t\tTe[B[i++] ^ ((C >> 8) & 255)] ^\n\t\tTd[B[i++] ^ ((C >> 16) & 255)] ^\n\t\tTc[B[i++] ^ (C >>> 24)] ^\n\t\tTb[B[i++]] ^ Ta[B[i++]] ^ T9[B[i++]] ^ T8[B[i++]] ^\n\t\tT7[B[i++]] ^ T6[B[i++]] ^ T5[B[i++]] ^ T4[B[i++]] ^\n\t\tT3[B[i++]] ^ T2[B[i++]] ^ T1[B[i++]] ^ T0[B[i++]];\n\tL += 15;\n\twhile(i < L) C = (C>>>8) ^ T0[(C^B[i++])&0xFF];\n\treturn ~C;\n}\n\nfunction crc32_str(str, seed) {\n\tvar C = seed ^ -1;\n\tfor(var i = 0, L = str.length, c = 0, d = 0; i < L;) {\n\t\tc = str.charCodeAt(i++);\n\t\tif(c < 0x80) {\n\t\t\tC = (C>>>8) ^ T0[(C^c)&0xFF];\n\t\t} else if(c < 0x800) {\n\t\t\tC = (C>>>8) ^ T0[(C ^ (192|((c>>6)&31)))&0xFF];\n\t\t\tC = (C>>>8) ^ T0[(C ^ (128|(c&63)))&0xFF];\n\t\t} else if(c >= 0xD800 && c < 0xE000) {\n\t\t\tc = (c&1023)+64; d = str.charCodeAt(i++)&1023;\n\t\t\tC = (C>>>8) ^ T0[(C ^ (240|((c>>8)&7)))&0xFF];\n\t\t\tC = (C>>>8) ^ T0[(C ^ (128|((c>>2)&63)))&0xFF];\n\t\t\tC = (C>>>8) ^ T0[(C ^ (128|((d>>6)&15)|((c&3)<<4)))&0xFF];\n\t\t\tC = (C>>>8) ^ T0[(C ^ (128|(d&63)))&0xFF];\n\t\t} else {\n\t\t\tC = (C>>>8) ^ T0[(C ^ (224|((c>>12)&15)))&0xFF];\n\t\t\tC = (C>>>8) ^ T0[(C ^ (128|((c>>6)&63)))&0xFF];\n\t\t\tC = (C>>>8) ^ T0[(C ^ (128|(c&63)))&0xFF];\n\t\t}\n\t}\n\treturn ~C;\n}\nCRC32.table = T0;\n// $FlowIgnore\nCRC32.bstr = crc32_bstr;\n// $FlowIgnore\nCRC32.buf = crc32_buf;\n// $FlowIgnore\nCRC32.str = crc32_str;\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY3JjLTMyL2NyYzMyLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLLElBQTJCO0FBQ2hDO0FBQ0EsSUFBSSxLQUFLLEVBUU47QUFDSCxHQUFHO0FBQ0gsb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxlQUFlLFVBQVU7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxZQUFZLFVBQVU7QUFDdEIsWUFBWSxVQUFVO0FBQ3RCO0FBQ0EsbUJBQW1CLFVBQVU7QUFDN0I7QUFDQTtBQUNBLFlBQVksU0FBUztBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLE1BQU07QUFDdkM7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsT0FBTyxNQUFNO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsOENBQThDLE1BQU07QUFDcEQ7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQSxJQUFJO0FBQ0osb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2xpZW50Ly4vbm9kZV9tb2R1bGVzL2NyYy0zMi9jcmMzMi5qcz8wNmQwIl0sInNvdXJjZXNDb250ZW50IjpbIi8qISBjcmMzMi5qcyAoQykgMjAxNC1wcmVzZW50IFNoZWV0SlMgLS0gaHR0cDovL3NoZWV0anMuY29tICovXG4vKiB2aW06IHNldCB0cz0yOiAqL1xuLypleHBvcnRlZCBDUkMzMiAqL1xudmFyIENSQzMyO1xuKGZ1bmN0aW9uIChmYWN0b3J5KSB7XG5cdC8qanNoaW50IGlnbm9yZTpzdGFydCAqL1xuXHQvKmVzbGludC1kaXNhYmxlICovXG5cdGlmKHR5cGVvZiBET19OT1RfRVhQT1JUX0NSQyA9PT0gJ3VuZGVmaW5lZCcpIHtcblx0XHRpZignb2JqZWN0JyA9PT0gdHlwZW9mIGV4cG9ydHMpIHtcblx0XHRcdGZhY3RvcnkoZXhwb3J0cyk7XG5cdFx0fSBlbHNlIGlmICgnZnVuY3Rpb24nID09PSB0eXBlb2YgZGVmaW5lICYmIGRlZmluZS5hbWQpIHtcblx0XHRcdGRlZmluZShmdW5jdGlvbiAoKSB7XG5cdFx0XHRcdHZhciBtb2R1bGUgPSB7fTtcblx0XHRcdFx0ZmFjdG9yeShtb2R1bGUpO1xuXHRcdFx0XHRyZXR1cm4gbW9kdWxlO1xuXHRcdFx0fSk7XG5cdFx0fSBlbHNlIHtcblx0XHRcdGZhY3RvcnkoQ1JDMzIgPSB7fSk7XG5cdFx0fVxuXHR9IGVsc2Uge1xuXHRcdGZhY3RvcnkoQ1JDMzIgPSB7fSk7XG5cdH1cblx0Lyplc2xpbnQtZW5hYmxlICovXG5cdC8qanNoaW50IGlnbm9yZTplbmQgKi9cbn0oZnVuY3Rpb24oQ1JDMzIpIHtcbkNSQzMyLnZlcnNpb24gPSAnMS4yLjInO1xuLypnbG9iYWwgSW50MzJBcnJheSAqL1xuZnVuY3Rpb24gc2lnbmVkX2NyY190YWJsZSgpIHtcblx0dmFyIGMgPSAwLCB0YWJsZSA9IG5ldyBBcnJheSgyNTYpO1xuXG5cdGZvcih2YXIgbiA9MDsgbiAhPSAyNTY7ICsrbil7XG5cdFx0YyA9IG47XG5cdFx0YyA9ICgoYyYxKSA/ICgtMzA2Njc0OTEyIF4gKGMgPj4+IDEpKSA6IChjID4+PiAxKSk7XG5cdFx0YyA9ICgoYyYxKSA/ICgtMzA2Njc0OTEyIF4gKGMgPj4+IDEpKSA6IChjID4+PiAxKSk7XG5cdFx0YyA9ICgoYyYxKSA/ICgtMzA2Njc0OTEyIF4gKGMgPj4+IDEpKSA6IChjID4+PiAxKSk7XG5cdFx0YyA9ICgoYyYxKSA/ICgtMzA2Njc0OTEyIF4gKGMgPj4+IDEpKSA6IChjID4+PiAxKSk7XG5cdFx0YyA9ICgoYyYxKSA/ICgtMzA2Njc0OTEyIF4gKGMgPj4+IDEpKSA6IChjID4+PiAxKSk7XG5cdFx0YyA9ICgoYyYxKSA/ICgtMzA2Njc0OTEyIF4gKGMgPj4+IDEpKSA6IChjID4+PiAxKSk7XG5cdFx0YyA9ICgoYyYxKSA/ICgtMzA2Njc0OTEyIF4gKGMgPj4+IDEpKSA6IChjID4+PiAxKSk7XG5cdFx0YyA9ICgoYyYxKSA/ICgtMzA2Njc0OTEyIF4gKGMgPj4+IDEpKSA6IChjID4+PiAxKSk7XG5cdFx0dGFibGVbbl0gPSBjO1xuXHR9XG5cblx0cmV0dXJuIHR5cGVvZiBJbnQzMkFycmF5ICE9PSAndW5kZWZpbmVkJyA/IG5ldyBJbnQzMkFycmF5KHRhYmxlKSA6IHRhYmxlO1xufVxuXG52YXIgVDAgPSBzaWduZWRfY3JjX3RhYmxlKCk7XG5mdW5jdGlvbiBzbGljZV9ieV8xNl90YWJsZXMoVCkge1xuXHR2YXIgYyA9IDAsIHYgPSAwLCBuID0gMCwgdGFibGUgPSB0eXBlb2YgSW50MzJBcnJheSAhPT0gJ3VuZGVmaW5lZCcgPyBuZXcgSW50MzJBcnJheSg0MDk2KSA6IG5ldyBBcnJheSg0MDk2KSA7XG5cblx0Zm9yKG4gPSAwOyBuICE9IDI1NjsgKytuKSB0YWJsZVtuXSA9IFRbbl07XG5cdGZvcihuID0gMDsgbiAhPSAyNTY7ICsrbikge1xuXHRcdHYgPSBUW25dO1xuXHRcdGZvcihjID0gMjU2ICsgbjsgYyA8IDQwOTY7IGMgKz0gMjU2KSB2ID0gdGFibGVbY10gPSAodiA+Pj4gOCkgXiBUW3YgJiAweEZGXTtcblx0fVxuXHR2YXIgb3V0ID0gW107XG5cdGZvcihuID0gMTsgbiAhPSAxNjsgKytuKSBvdXRbbiAtIDFdID0gdHlwZW9mIEludDMyQXJyYXkgIT09ICd1bmRlZmluZWQnID8gdGFibGUuc3ViYXJyYXkobiAqIDI1NiwgbiAqIDI1NiArIDI1NikgOiB0YWJsZS5zbGljZShuICogMjU2LCBuICogMjU2ICsgMjU2KTtcblx0cmV0dXJuIG91dDtcbn1cbnZhciBUVCA9IHNsaWNlX2J5XzE2X3RhYmxlcyhUMCk7XG52YXIgVDEgPSBUVFswXSwgIFQyID0gVFRbMV0sICBUMyA9IFRUWzJdLCAgVDQgPSBUVFszXSwgIFQ1ID0gVFRbNF07XG52YXIgVDYgPSBUVFs1XSwgIFQ3ID0gVFRbNl0sICBUOCA9IFRUWzddLCAgVDkgPSBUVFs4XSwgIFRhID0gVFRbOV07XG52YXIgVGIgPSBUVFsxMF0sIFRjID0gVFRbMTFdLCBUZCA9IFRUWzEyXSwgVGUgPSBUVFsxM10sIFRmID0gVFRbMTRdO1xuZnVuY3Rpb24gY3JjMzJfYnN0cihic3RyLCBzZWVkKSB7XG5cdHZhciBDID0gc2VlZCBeIC0xO1xuXHRmb3IodmFyIGkgPSAwLCBMID0gYnN0ci5sZW5ndGg7IGkgPCBMOykgQyA9IChDPj4+OCkgXiBUMFsoQ15ic3RyLmNoYXJDb2RlQXQoaSsrKSkmMHhGRl07XG5cdHJldHVybiB+Qztcbn1cblxuZnVuY3Rpb24gY3JjMzJfYnVmKEIsIHNlZWQpIHtcblx0dmFyIEMgPSBzZWVkIF4gLTEsIEwgPSBCLmxlbmd0aCAtIDE1LCBpID0gMDtcblx0Zm9yKDsgaSA8IEw7KSBDID1cblx0XHRUZltCW2krK10gXiAoQyAmIDI1NSldIF5cblx0XHRUZVtCW2krK10gXiAoKEMgPj4gOCkgJiAyNTUpXSBeXG5cdFx0VGRbQltpKytdIF4gKChDID4+IDE2KSAmIDI1NSldIF5cblx0XHRUY1tCW2krK10gXiAoQyA+Pj4gMjQpXSBeXG5cdFx0VGJbQltpKytdXSBeIFRhW0JbaSsrXV0gXiBUOVtCW2krK11dIF4gVDhbQltpKytdXSBeXG5cdFx0VDdbQltpKytdXSBeIFQ2W0JbaSsrXV0gXiBUNVtCW2krK11dIF4gVDRbQltpKytdXSBeXG5cdFx0VDNbQltpKytdXSBeIFQyW0JbaSsrXV0gXiBUMVtCW2krK11dIF4gVDBbQltpKytdXTtcblx0TCArPSAxNTtcblx0d2hpbGUoaSA8IEwpIEMgPSAoQz4+PjgpIF4gVDBbKENeQltpKytdKSYweEZGXTtcblx0cmV0dXJuIH5DO1xufVxuXG5mdW5jdGlvbiBjcmMzMl9zdHIoc3RyLCBzZWVkKSB7XG5cdHZhciBDID0gc2VlZCBeIC0xO1xuXHRmb3IodmFyIGkgPSAwLCBMID0gc3RyLmxlbmd0aCwgYyA9IDAsIGQgPSAwOyBpIDwgTDspIHtcblx0XHRjID0gc3RyLmNoYXJDb2RlQXQoaSsrKTtcblx0XHRpZihjIDwgMHg4MCkge1xuXHRcdFx0QyA9IChDPj4+OCkgXiBUMFsoQ15jKSYweEZGXTtcblx0XHR9IGVsc2UgaWYoYyA8IDB4ODAwKSB7XG5cdFx0XHRDID0gKEM+Pj44KSBeIFQwWyhDIF4gKDE5MnwoKGM+PjYpJjMxKSkpJjB4RkZdO1xuXHRcdFx0QyA9IChDPj4+OCkgXiBUMFsoQyBeICgxMjh8KGMmNjMpKSkmMHhGRl07XG5cdFx0fSBlbHNlIGlmKGMgPj0gMHhEODAwICYmIGMgPCAweEUwMDApIHtcblx0XHRcdGMgPSAoYyYxMDIzKSs2NDsgZCA9IHN0ci5jaGFyQ29kZUF0KGkrKykmMTAyMztcblx0XHRcdEMgPSAoQz4+PjgpIF4gVDBbKEMgXiAoMjQwfCgoYz4+OCkmNykpKSYweEZGXTtcblx0XHRcdEMgPSAoQz4+PjgpIF4gVDBbKEMgXiAoMTI4fCgoYz4+MikmNjMpKSkmMHhGRl07XG5cdFx0XHRDID0gKEM+Pj44KSBeIFQwWyhDIF4gKDEyOHwoKGQ+PjYpJjE1KXwoKGMmMyk8PDQpKSkmMHhGRl07XG5cdFx0XHRDID0gKEM+Pj44KSBeIFQwWyhDIF4gKDEyOHwoZCY2MykpKSYweEZGXTtcblx0XHR9IGVsc2Uge1xuXHRcdFx0QyA9IChDPj4+OCkgXiBUMFsoQyBeICgyMjR8KChjPj4xMikmMTUpKSkmMHhGRl07XG5cdFx0XHRDID0gKEM+Pj44KSBeIFQwWyhDIF4gKDEyOHwoKGM+PjYpJjYzKSkpJjB4RkZdO1xuXHRcdFx0QyA9IChDPj4+OCkgXiBUMFsoQyBeICgxMjh8KGMmNjMpKSkmMHhGRl07XG5cdFx0fVxuXHR9XG5cdHJldHVybiB+Qztcbn1cbkNSQzMyLnRhYmxlID0gVDA7XG4vLyAkRmxvd0lnbm9yZVxuQ1JDMzIuYnN0ciA9IGNyYzMyX2JzdHI7XG4vLyAkRmxvd0lnbm9yZVxuQ1JDMzIuYnVmID0gY3JjMzJfYnVmO1xuLy8gJEZsb3dJZ25vcmVcbkNSQzMyLnN0ciA9IGNyYzMyX3N0cjtcbn0pKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/crc-32/crc32.js\n");

/***/ })

};
;