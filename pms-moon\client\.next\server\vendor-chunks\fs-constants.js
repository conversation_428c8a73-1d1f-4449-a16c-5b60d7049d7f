/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fs-constants";
exports.ids = ["vendor-chunks/fs-constants"];
exports.modules = {

/***/ "(ssr)/./node_modules/fs-constants/index.js":
/*!********************************************!*\
  !*** ./node_modules/fs-constants/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = (__webpack_require__(/*! fs */ \"fs\").constants) || __webpack_require__(/*! constants */ \"constants\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnMtY29uc3RhbnRzL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBLGlCQUFpQiwrQ0FBdUIsSUFBSSxtQkFBTyxDQUFDLDRCQUFXIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2xpZW50Ly4vbm9kZV9tb2R1bGVzL2ZzLWNvbnN0YW50cy9pbmRleC5qcz8zYmQwIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnZnMnKS5jb25zdGFudHMgfHwgcmVxdWlyZSgnY29uc3RhbnRzJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fs-constants/index.js\n");

/***/ })

};
;