"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import {
  DndContext,
  type DragEndEvent,
  DragOverlay,
  type DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { TicketFilters as TicketFiltersComponent } from "./components/ticket-filters";
import { Button } from "@/components/ui/button";
import { RefreshCw, Layout, Sidebar, Minus, List, User } from "lucide-react";
import {
  Column,
  Ticket,
  TicketFilters,
  TicketStatus,
  User as UserType,
  Tag,
  Pipeline,
} from "./ticket";
import {
  fetchTickets,
  bulkDeleteTickets,
} from "./tickets";
import { BulkActions } from "./components/bulk-action";
import { KanbanColumn } from "./components/kanban-column";
import { TicketCard } from "./components/ticket-card";
import { TicketModal } from "./components/ticket-modal";
import { TicketSidebar } from "./components/ticket-sidebar";
import { getAllData } from "@/lib/helpers";
import { employee_routes, ticket_routes } from "@/lib/routePath";
import BreadCrumbs from "@/app/_component/BreadCrumbs";
import { TicketProvider, TicketContext } from "./TicketContext";

function TicketsPage() {
  const router = useRouter();
  const { tickets, setTickets, currentUser, setCurrentUser } = React.useContext(TicketContext);
  const [filteredTickets, setFilteredTickets] = useState<Ticket[]>([]);
  const [selectedTickets, setSelectedTickets] = useState<string[]>([]);
  const [activeTicket, setActiveTicket] = useState<Ticket | null>(null);
  const [viewMode, setViewMode] = useState<"modal" | "sidebar">("modal");
  const [isLoading, setIsLoading] = useState(true);
  const [draggedTicket, setDraggedTicket] = useState<Ticket | null>(null);
  const [tab, setTab] = useState<'all' | 'mine'>('all');

  const hasLoadedTickets = useRef(false);

  const [filters, setFilters] = useState<TicketFilters>({
    search: "",
    stageIds: [],
    assignedTo: [],
    priority: [],
    tags: [],
    dateRange: {},
  });

  const [tags, setTags] = useState<Tag[]>([]);

  const pointerSensor = useSensor(PointerSensor, {
    activationConstraint: {
      distance: 15,
    },
  });
  const sensors = useSensors(pointerSensor);

  const columnColors = [
    { bg: "bg-red-50", badge: "bg-red-200", badgeText: "text-red-800" },
    { bg: "bg-pink-50", badge: "bg-pink-200", badgeText: "text-pink-800" },
    {
      bg: "bg-purple-50",
      badge: "bg-purple-200",
      badgeText: "text-purple-800",
    },
    {
      bg: "bg-yellow-50",
      badge: "bg-yellow-200",
      badgeText: "text-yellow-800",
    },
    {
      bg: "bg-orange-50",
      badge: "bg-orange-200",
      badgeText: "text-orange-800",
    },
    { bg: "bg-green-50", badge: "bg-green-200", badgeText: "text-green-800" },
    { bg: "bg-blue-50", badge: "bg-blue-200", badgeText: "text-blue-800" },
    { bg: "bg-teal-50", badge: "bg-teal-200", badgeText: "text-teal-800" },
  ];

  const loadTickets = useCallback(async () => {
    setIsLoading(true);
    try {
      const data = await fetchTickets();
      setTickets(data);
    } catch (error) {
      console.error("Failed to fetch tickets:", error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (hasLoadedTickets.current) return;
    hasLoadedTickets.current = true;

    loadTickets();
    return () => {};
  }, [loadTickets]);

  useEffect(() => {
    let filtered = tickets;

    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(
        (ticket) =>
          ticket.title.toLowerCase().includes(searchLower) ||
          ticket.description.toLowerCase().includes(searchLower) ||
          (ticket.currentStage?.assignedTo?.toLowerCase?.() || "").includes(
            searchLower
          ) ||
          ticket.tags.some((tag) =>
            (tag.name || tag.tagName || "").toLowerCase().includes(searchLower)
          )
      );
    }

    if (tab === 'mine' && currentUser) {
      filtered = filtered.filter(ticket => {
        const assignedTo = ticket.currentStage?.assignedTo;
        return String(assignedTo) === String(currentUser.id) || String(assignedTo) === String(currentUser.username);
      });
    }
    if (filters.assignedTo.length > 0) {
      filtered = filtered.filter(
        (ticket) =>
          ticket.currentStage &&
          filters.assignedTo.includes(String(ticket.currentStage.assignedTo))
      );
    }

    if (filters.priority.length > 0) {
      filtered = filtered.filter((ticket) =>
        filters.priority.includes(ticket.priority)
      );
    }

    if (filters.tags.length > 0) {
      filtered = filtered.filter((ticket) =>
        ticket.tags.some((tag) => filters.tags.includes(tag.id))
      );
    }

    if (filters.dateRange.from || filters.dateRange.to) {
      filtered = filtered.filter((ticket) => {
        if (!ticket.dueDate) return false;
        const dueDate = new Date(ticket.dueDate);
        if (filters.dateRange.from && dueDate < filters.dateRange.from)
          return false;
        if (filters.dateRange.to && dueDate > filters.dateRange.to)
          return false;
        return true;
      });
    }
    if (filters.stageIds.length > 0) {
      filtered = filtered.filter((ticket) => {
        const stageId =
          ticket.currentStage?.pipelineStageId ||
          ticket.pipeline.stages?.[0]?.id;
        return filters.stageIds.includes(stageId);
      });
    }

    setFilteredTickets(filtered);
  }, [tickets, filters, tab, currentUser]);

  const masterPipelineOrder = React.useMemo(() => {
    const seen = new Set();
    return tickets
      .map(t => t.pipeline)
      .filter(p => {
        if (!p || seen.has(p.id)) return false;
        seen.add(p.id);
        return true;
      });
  }, [tickets]);

  const pipelines = React.useMemo(() => {
    const ticketsByPipeline: Record<string, Ticket[]> = {};
    filteredTickets.forEach(ticket => {
      const pid = ticket.pipeline.id;
      if (!ticketsByPipeline[pid]) ticketsByPipeline[pid] = [];
      ticketsByPipeline[pid].push(ticket);
    });
    return masterPipelineOrder
      .filter(p => ticketsByPipeline[p.id])
      .map(p => ({ pipeline: p, tickets: ticketsByPipeline[p.id] }));
  }, [filteredTickets, masterPipelineOrder]);

  const getCurrentStageId = (ticket: Ticket, pipeline: Pipeline) => {
    return ticket.currentStage?.pipelineStageId || pipeline.stages?.[0]?.id;
  };

  const handleDragStart = (event: DragStartEvent) => {
    const ticket = tickets.find((t) => t.id === event.active.id);
    setDraggedTicket(ticket || null);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedTicket(null);

    if (!over || active.id === over.id) return;

    const ticketId = active.id as string;
    const newStageId = over.id as string;

    const ticket = tickets.find((t) => t.id === ticketId);
    if (!ticket) {
      return;
    }

    const newCurrentStage = ticket.stages?.find(
      (stage) => stage.pipelineStageId === newStageId
    );
    if (!newCurrentStage) {
      return;
    }

    setTickets((prev) =>
      prev.map((t) =>
        t.id === ticketId ? { ...t, currentStage: newCurrentStage } : t
      )
    );

    // Use whatever identifier is available for the user
    let userId =
      currentUser?.username ||
      currentUser?.id ||
      currentUser?.email ||
      (currentUser?.success && currentUser?.message ? "admin" : "unknown");

    try {
      const requestBody = {
        ticketId,
        ticketStageId: newStageId,
        createdBy: userId,
      };

      const response = await fetch(ticket_routes.UPDATE_TICKET(ticketId), {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      const responseData = await response.json();
      // handle response if needed
    } catch (error) {
      console.error("Failed to persist stage change:", error);
    }
  };

  const handleSelectTicket = (ticketId: string, selected: boolean) => {
    setSelectedTickets((prev) =>
      selected ? [...prev, ticketId] : prev.filter((id) => id !== ticketId)
    );
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelectedTickets(filteredTickets.map((t) => t.id));
    } else {
      setSelectedTickets([]);
    }
  };

  const handleBulkMove = async (stageId: string) => {
    try {
      setTickets((prev) => {
        const updated = prev.map((ticket) => {
          if (selectedTickets.includes(ticket.id)) {
            const newCurrentStage = ticket.stages?.find(
              (stage) => stage.pipelineStageId === stageId
            );
            if (!newCurrentStage) return ticket;
            return { ...ticket, currentStage: newCurrentStage };
          }
          return ticket;
        });
        return updated;
      });
      const payload = {
        tickets: selectedTickets.map((ticketId) => ({
          ticketId,
          ticketStageId: stageId,
          createdBy: currentUser?.username,
        })),
      };
      
      const response = await fetch(ticket_routes.BULK_UPDATE_TICKETS, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      const responseData = await response.json().catch(() => ({}));
      
      setSelectedTickets([]);
    } catch (error) {
      console.error("Failed to bulk move tickets:", error);
    }
  };

  const handleBulkTag = async (tagId: string) => {
    const tag = tickets.find((t) => t.id === tagId);
    if (!tag) return;

    try {
      setTickets((prev) =>
        prev.map((ticket) =>
          selectedTickets.includes(ticket.id)
            ? {
                ...ticket,
                tags: [...ticket.tags.filter((t) => t.id !== tagId), tag],
              }
            : ticket
        )
      );
      setSelectedTickets([]);
    } catch (error) {
      console.error("Failed to bulk tag tickets:", error);
    }
  };

  const handleBulkDelete = async () => {
    if (
      !confirm(
        `Are you sure you want to delete ${selectedTickets.length} tickets?`
      )
    )
      return;
    try {
      await bulkDeleteTickets(selectedTickets, currentUser?.username);
      setTickets((prev) =>
        prev.filter((ticket) => !selectedTickets.includes(ticket.id))
      );
      setSelectedTickets([]);
    } catch (error) {
      console.error("Failed to bulk delete tickets:", error);
    }
  };

  const handleTicketClick = (ticket: Ticket) => {
    const latestTicket = tickets.find(t => t.id === ticket.id);
    setActiveTicket(latestTicket || ticket);
  };

  const handleOpenInNewTab = (ticketId: string) => {
    window.open(`/pms/manage_tickets/${ticketId}`, "_blank");
  };

  const handleCloseDetail = () => {
    setActiveTicket(null);
  };

  const handleTagsUpdated = async () => {
    console.log("Tags updated - using local state updates instead of refetching all tickets");
  };

  console.log('currentUser', currentUser);
  console.log('tickets', tickets.map(t => ({
    id: t.id,
    assignedTo: t.currentStage?.assignedTo
  })));

  if (isLoading || currentUser === null) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-full mx-auto">
        <BreadCrumbs
          breadcrumblist={[
            { link: "/pms", name: "Dashboard" },
            { link: "/pms/manage_tickets", name: "Tickets" },
          ]}
        />
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Tickets</h1>
            <p className="text-gray-600 mt-1">
              Manage and track your tickets here
            </p>
          </div>

          <div className="flex items-center space-x-2 mt-4 sm:mt-0">
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setViewMode(viewMode === "modal" ? "sidebar" : "modal")
              }
            >
              {viewMode === "modal" ? (
                <Sidebar className="mr-2 h-4 w-4" />
              ) : (
                <Layout className="mr-2 h-4 w-4" />
              )}
              {viewMode === "modal" ? "Sidebar View" : "Modal View"}
            </Button>

            <Button variant="outline" size="sm" onClick={loadTickets}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        <div className="mb-6">
          <TicketFiltersComponent
            filters={filters}
            onFiltersChange={setFilters}
            stages={Array.from(
              new Map(
                tickets
                  .flatMap((t) => t.pipeline.stages)
                  .map((stage) => [stage.id, stage])
              ).values()
            )}
            users={[]}
          />
        </div>

        {/* Notion-style Tab Bar BELOW the filters/search bar and ABOVE the pipeline name */}
        <div className="flex gap-2 mb-6">
          <button
            className={`flex items-center gap-1 px-3 py-1.5 rounded-full text-sm font-medium transition-colors border-none focus:outline-none focus:ring-2 focus:ring-gray-300 ${tab === 'all' ? 'bg-gray-800 text-white shadow' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
            onClick={() => setTab('all')}
          >
            <List className="w-3 h-3" />
            All Tickets
          </button>
          <button
            className={`flex items-center gap-1 px-3 py-1.5 rounded-full text-sm font-medium transition-colors border-none focus:outline-none focus:ring-2 focus:ring-gray-300 ${tab === 'mine' ? 'bg-gray-800 text-white shadow' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
            onClick={() => setTab('mine')}
          >
            <User className="w-3 h-3" />
            Mine
          </button>
        </div>

        <BulkActions
          selectedCount={selectedTickets.length}
          onBulkMove={handleBulkMove}
          onBulkTag={handleBulkTag}
          onBulkDelete={handleBulkDelete}
          onClearSelection={() => setSelectedTickets([])}
          stages={(() => {
            const firstSelected = tickets.find(
              (t) => t.id === selectedTickets[0]
            );
            return firstSelected?.pipeline?.stages || [];
          })()}
          users={[]}
        />

        {/* Empty state for 'Mine' tab with no tickets */}
        {tab === 'mine' && filteredTickets.length === 0 && (
          <div className="flex flex-col items-center justify-center py-24">
            <User className="w-16 h-16 text-gray-300 mb-4" />
            <p className="text-lg text-gray-500">No tickets assigned to you yet.</p>
          </div>
        )}

        {/* Render pipelines only if there are tickets */}
        {filteredTickets.length > 0 && pipelines.map(({ pipeline, tickets }, pipelineIdx) => {
          const columns = Array.isArray(pipeline.stages)
            ? pipeline.stages.map((stage) => ({
                ...stage,
                tickets: tickets.filter(
                  (ticket) => getCurrentStageId(ticket, pipeline) === stage.id
                ),
              }))
            : [];
          return (
            <div key={pipeline.id} className="mb-12">
              <h2 className="flex items-center gap-2 text-2xl font-semibold mb-4">
                <span
                  className={`w-2 h-8 rounded-full ${columnColors[0].badge}`}
                ></span>
                {pipeline.name}
              </h2>
              <DndContext
                sensors={sensors}
                onDragStart={handleDragStart}
                onDragEnd={handleDragEnd}
              >
                <div className="flex gap-x-6 overflow-x-auto pb-6 whitespace-nowrap">
                  {columns.map((column, idx) => (
                    <KanbanColumn
                      key={column.id}
                      id={column.id}
                      title={column.name}
                      bgColor={columnColors[idx % columnColors.length].bg}
                      badgeColor={columnColors[idx % columnColors.length].badge}
                      badgeTextColor={
                        columnColors[idx % columnColors.length].badgeText
                      }
                      tickets={column.tickets}
                      selectedTickets={selectedTickets}
                      onSelectTicket={handleSelectTicket}
                      onTicketClick={handleTicketClick}
                      onOpenInNewTab={handleOpenInNewTab}
                    />
                  ))}
                </div>
                <DragOverlay>
                  {draggedTicket && (
                    <TicketCard
                      ticket={draggedTicket}
                      isSelected={false}
                      onSelect={() => {}}
                      onClick={() => {}}
                      onOpenInNewTab={() => {}}
                      isDragging
                    />
                  )}
                </DragOverlay>
              </DndContext>
            </div>
          );
        })}

        {viewMode === "modal" ? (
          <TicketModal
            ticket={activeTicket}
            isOpen={!!activeTicket && viewMode === "modal"}
            onClose={handleCloseDetail}
            onOpenInNewTab={handleOpenInNewTab}
            onTagsUpdated={handleTagsUpdated}
          />
        ) : (
          <TicketSidebar
            ticket={activeTicket}
            isOpen={!!activeTicket && viewMode === "sidebar"}
            onClose={handleCloseDetail}
            onOpenInNewTab={handleOpenInNewTab}
            onTagsUpdated={handleTagsUpdated}
          />
        )}
      </div>
    </div>
  );
}

export default function TicketsPageWithProvider(props) {
  return (
    <TicketProvider>
      <TicketsPage {...props} />
    </TicketProvider>
  );
}
