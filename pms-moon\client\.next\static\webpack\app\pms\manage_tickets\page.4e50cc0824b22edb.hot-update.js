"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pms/manage_tickets/page",{

/***/ "(app-pages-browser)/./app/pms/manage_tickets/components/ticket-sidebar.tsx":
/*!**************************************************************!*\
  !*** ./app/pms/manage_tickets/components/ticket-sidebar.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TicketSidebar: function() { return /* binding */ TicketSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./components/ui/sheet.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _comment_section__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./comment-section */ \"(app-pages-browser)/./app/pms/manage_tickets/components/comment-section.tsx\");\n/* harmony import */ var _tag_manager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./tag-manager */ \"(app-pages-browser)/./app/pms/manage_tickets/components/tag-manager.tsx\");\n/* harmony import */ var _TicketContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../TicketContext */ \"(app-pages-browser)/./app/pms/manage_tickets/TicketContext.tsx\");\n/* harmony import */ var _activity_section__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./activity-section */ \"(app-pages-browser)/./app/pms/manage_tickets/components/activity-section.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _app_pms_manage_tickets_tickets__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/pms/manage_tickets/tickets */ \"(app-pages-browser)/./app/pms/manage_tickets/tickets.ts\");\n/* __next_internal_client_entry_do_not_use__ TicketSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TicketSidebar(param) {\n    let { ticket: initialTicket, isOpen, onClose, onOpenInNewTab, onTagsUpdated } = param;\n    var _ticket_pipeline, _ticket_pipeline1;\n    _s();\n    // All hooks must be called at the top, before any early returns\n    const [commentsCount, setCommentsCount] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(0);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(\"details\");\n    const { users, currentUser, setTickets, tickets } = (0,react__WEBPACK_IMPORTED_MODULE_7__.useContext)(_TicketContext__WEBPACK_IMPORTED_MODULE_10__.TicketContext);\n    const hasLoadedCommentsCount = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n    // Inline edit state for priority\n    const [editingPriority, setEditingPriority] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    var _initialTicket_priority;\n    const [priorityValue, setPriorityValue] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)((_initialTicket_priority = initialTicket === null || initialTicket === void 0 ? void 0 : initialTicket.priority) !== null && _initialTicket_priority !== void 0 ? _initialTicket_priority : \"low\");\n    const [priorityLoading, setPriorityLoading] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [priorityError, setPriorityError] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(\"\");\n    // Now, after all hooks, handle ticket logic\n    let ticket = tickets.find((t)=>t.id === (initialTicket === null || initialTicket === void 0 ? void 0 : initialTicket.id)) || initialTicket;\n    if (ticket && ticket.currentStage && ((_ticket_pipeline = ticket.pipeline) === null || _ticket_pipeline === void 0 ? void 0 : _ticket_pipeline.stages)) {\n        const pipelineStage = ticket.pipeline.stages.find((ps)=>ps.id === ticket.currentStage.pipelineStageId);\n        if (pipelineStage && !ticket.currentStage.name) {\n            ticket = {\n                ...ticket,\n                currentStage: {\n                    ...ticket.currentStage,\n                    name: pipelineStage.name\n                }\n            };\n        }\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        async function fetchCommentsCount() {\n            if (!ticket) return;\n            try {\n                var _data_data;\n                const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_6__.comment_routes.GET_COMMENTS_BY_TICKET(ticket.id));\n                const data = await res.json();\n                setCommentsCount(((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.length) || 0);\n            } catch (e) {\n                setCommentsCount(0);\n            }\n        }\n        if (isOpen && ticket && hasLoadedCommentsCount.current !== ticket.id) {\n            hasLoadedCommentsCount.current = ticket.id;\n            fetchCommentsCount();\n        }\n        if (!isOpen) {\n            hasLoadedCommentsCount.current = null;\n        }\n    }, [\n        isOpen,\n        ticket\n    ]);\n    if (!ticket) return null;\n    const priorityColors = {\n        low: \"bg-gray-100 text-gray-800\",\n        medium: \"bg-blue-100 text-blue-800\",\n        high: \"bg-orange-100 text-orange-800\",\n        urgent: \"bg-red-100 text-red-800\"\n    };\n    const currentStage = ticket.currentStage;\n    const assignedUser = currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedUser;\n    let assignedToDisplay;\n    if (currentUser && ((currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedTo) === currentUser.id || (currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedTo) === currentUser.username)) {\n        assignedToDisplay = \"You\";\n    } else if (assignedUser) {\n        assignedToDisplay = assignedUser.username || assignedUser.id;\n    } else {\n        assignedToDisplay = (currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedTo) || \"Unassigned\";\n    }\n    const badgeColors = [\n        \"bg-gray-200 text-gray-800\",\n        \"bg-blue-200 text-blue-800\",\n        \"bg-green-200 text-green-800\",\n        \"bg-yellow-200 text-yellow-800\",\n        \"bg-purple-200 text-purple-800\",\n        \"bg-pink-200 text-pink-800\",\n        \"bg-orange-200 text-orange-800\",\n        \"bg-red-200 text-red-800\"\n    ];\n    let stageColor = \"bg-gray-200 text-gray-800\";\n    if ((_ticket_pipeline1 = ticket.pipeline) === null || _ticket_pipeline1 === void 0 ? void 0 : _ticket_pipeline1.stages) {\n        const idx = ticket.pipeline.stages.findIndex((s)=>{\n            var _ticket_currentStage;\n            return s.id === ((_ticket_currentStage = ticket.currentStage) === null || _ticket_currentStage === void 0 ? void 0 : _ticket_currentStage.pipelineStageId);\n        });\n        if (idx !== -1) {\n            stageColor = badgeColors[idx % badgeColors.length];\n        }\n    }\n    const handleCommentAdded = ()=>{\n        setCommentsCount((prev)=>prev + 1);\n    };\n    const handleTagsUpdated = ()=>{\n        onTagsUpdated === null || onTagsUpdated === void 0 ? void 0 : onTagsUpdated();\n    };\n    // Update ticket API call\n    async function updateTicketField(field, value) {\n        setPriorityLoading(true);\n        setPriorityError(\"\");\n        try {\n            const updated = await (0,_app_pms_manage_tickets_tickets__WEBPACK_IMPORTED_MODULE_13__.updateTicket)(ticket.id, {\n                [field]: value\n            }, String(currentUser === null || currentUser === void 0 ? void 0 : currentUser.id));\n            setPriorityValue(updated.priority);\n            setTickets((prev)=>prev.map((t)=>t.id === ticket.id ? updated : t));\n            setEditingPriority(false);\n        } catch (e) {\n            setPriorityError(e.message || \"Error updating priority\");\n        } finally{\n            setPriorityLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.Sheet, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetContent, {\n            className: \"w-full sm:max-w-lg overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 pr-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetTitle, {\n                                        className: \"text-lg mb-2 leading-tight\",\n                                        children: ticket.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap items-center gap-2\",\n                                        children: [\n                                            editingPriority ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                                        value: priorityValue,\n                                                        onValueChange: (val)=>updateTicketField(\"priority\", val),\n                                                        disabled: priorityLoading,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                                className: priorityColors[priorityValue] + \" min-w-[100px]\",\n                                                                children: [\n                                                                    priorityLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"animate-spin h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 161,\n                                                                        columnNumber: 44\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"mr-1 h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 161,\n                                                                        columnNumber: 91\n                                                                    }, this),\n                                                                    priorityValue\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                        value: \"low\",\n                                                                        children: \"Low\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 165,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                        value: \"medium\",\n                                                                        children: \"Medium\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 166,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                        value: \"high\",\n                                                                        children: \"High\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 167,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                        value: \"urgent\",\n                                                                        children: \"Urgent\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 168,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    priorityError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-red-600 ml-2\",\n                                                        children: priorityError\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 39\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                className: priorityColors[priorityValue],\n                                                onClick: ()=>setEditingPriority(true),\n                                                style: {\n                                                    cursor: \"pointer\"\n                                                },\n                                                title: \"Click to edit\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-1 h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    priorityValue\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentStage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                className: stageColor,\n                                                variant: \"secondary\",\n                                                children: currentStage.name || currentStage.pipelineStageId\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 19\n                                            }, this),\n                                            ticket.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                    className: \"\".concat(tag.color, \" text-xs\"),\n                                                    children: tag.tagName || tag.name\n                                                }, tag.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>onOpenInNewTab(ticket.id),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                        value: activeTab,\n                        onValueChange: setActiveTab,\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                className: \"grid w-full grid-cols-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"details\",\n                                        className: \"text-xs\",\n                                        children: \"Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"comments\",\n                                        className: \"text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Comments\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-1 text-blue-600 font-bold\",\n                                                children: [\n                                                    \"(\",\n                                                    commentsCount,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"tags\",\n                                        className: \"text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Tags\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"activity\",\n                                        className: \"text-xs\",\n                                        children: \"Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"details\",\n                                className: \"space-y-6 mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-3\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 text-sm leading-relaxed\",\n                                                children: ticket.description || \"No description provided\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                        children: \"Assigned To\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: assignedUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                                    className: \"h-5 w-5\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.AvatarImage, {\n                                                                            src: assignedUser.avatar || \" \",\n                                                                            alt: assignedToDisplay\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                            lineNumber: 232,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                                            className: \"text-xs\",\n                                                                            children: assignedUser.username ? assignedUser.username[0].toUpperCase() : \"\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                            lineNumber: 233,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: assignedToDisplay\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: assignedToDisplay\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentStage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                        children: \"Due Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm \".concat(currentStage.dueAt && new Date(currentStage.dueAt) < new Date() ? \"text-red-600\" : \"\"),\n                                                                children: currentStage.dueAt ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_20__.format)(new Date(currentStage.dueAt), \"PPP\") : \"No due date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                        children: \"Created\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_20__.format)(new Date(ticket.createdAt), \"PPP\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                        children: \"Last Updated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_20__.format)(new Date(ticket.updatedAt), \"PPP\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"comments\",\n                                className: \"space-y-4 mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_comment_section__WEBPACK_IMPORTED_MODULE_8__.CommentSection, {\n                                    ticketId: ticket.id,\n                                    createdBy: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.username) || \"\",\n                                    setCommentsCount: setCommentsCount,\n                                    onCommentsChange: (newComments)=>{\n                                        setTickets((prev)=>prev.map((t)=>t.id === ticket.id ? {\n                                                    ...t,\n                                                    comments: newComments\n                                                } : t));\n                                    },\n                                    isActive: activeTab === \"comments\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"tags\",\n                                className: \"space-y-4 mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tag_manager__WEBPACK_IMPORTED_MODULE_9__.TagManager, {\n                                    ticketId: ticket.id,\n                                    assignedTags: ticket.tags,\n                                    onTagsUpdated: handleTagsUpdated,\n                                    onTagsChange: (newTags)=>{\n                                        setTickets((prev)=>prev.map((t)=>t.id === ticket.id ? {\n                                                    ...t,\n                                                    tags: newTags\n                                                } : t));\n                                    },\n                                    createdBy: ticket.createdBy || \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"activity\",\n                                className: \"space-y-4 mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_activity_section__WEBPACK_IMPORTED_MODULE_11__.ActivitySection, {\n                                    ticketId: ticket.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, this);\n}\n_s(TicketSidebar, \"kJn1Xh+J0Vgr+I6v1QlTJn7sRuU=\");\n_c = TicketSidebar;\nvar _c;\n$RefreshReg$(_c, \"TicketSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pms/manage_tickets/components/ticket-sidebar.tsx\n"));

/***/ })

});