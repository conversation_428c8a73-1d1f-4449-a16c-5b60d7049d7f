"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pms/manage_tickets/page",{

/***/ "(app-pages-browser)/./app/pms/manage_tickets/tickets.ts":
/*!*******************************************!*\
  !*** ./app/pms/manage_tickets/tickets.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bulkDeleteTickets: function() { return /* binding */ bulkDeleteTickets; },\n/* harmony export */   deleteTicket: function() { return /* binding */ deleteTicket; },\n/* harmony export */   fetchTags: function() { return /* binding */ fetchTags; },\n/* harmony export */   fetchTicket: function() { return /* binding */ fetchTicket; },\n/* harmony export */   fetchTickets: function() { return /* binding */ fetchTickets; },\n/* harmony export */   fetchUsers: function() { return /* binding */ fetchUsers; },\n/* harmony export */   updateTicket: function() { return /* binding */ updateTicket; },\n/* harmony export */   updateTicketStatus: function() { return /* binding */ updateTicketStatus; }\n/* harmony export */ });\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n\n// Fetch all tickets\nasync function fetchTickets() {\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.ticket_routes.GET_TICKETS, {\n        method: \"GET\"\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch tickets\");\n    const data = await res.json();\n    // Map API response to UI Ticket interface\n    return data.data.map((apiTicket)=>{\n        // Find the correct current stage object\n        let currentStage = undefined;\n        if (apiTicket.currentStageId && Array.isArray(apiTicket.stages)) {\n            currentStage = apiTicket.stages.find((stage)=>stage.pipelineStageId === apiTicket.currentStageId);\n        } else if (apiTicket.currentStage) {\n            currentStage = apiTicket.currentStage;\n        } else if (Array.isArray(apiTicket.stages) && apiTicket.stages.length > 0) {\n            currentStage = apiTicket.stages[apiTicket.stages.length - 1];\n        }\n        return {\n            id: apiTicket.id,\n            title: apiTicket.title || \"Ticket for Invoice \".concat(apiTicket.workItemId),\n            description: apiTicket.description || \"\",\n            status: \"\",\n            priority: (apiTicket.priority || \"low\").toLowerCase(),\n            dueDate: Array.isArray(apiTicket.stages) && apiTicket.stages.length > 0 ? apiTicket.stages[apiTicket.stages.length - 1].dueAt : null,\n            createdAt: apiTicket.createdAt ? new Date(apiTicket.createdAt) : new Date(),\n            updatedAt: apiTicket.updatedAt ? new Date(apiTicket.updatedAt) : new Date(),\n            tags: apiTicket.tags || [],\n            comments: apiTicket.comments || [],\n            pipeline: apiTicket.pipeline || {},\n            currentStage: currentStage,\n            stages: apiTicket.stages || [],\n            createdBy: apiTicket.createdBy || \"\",\n            owner: apiTicket.owner || \"\"\n        };\n    });\n}\n// Fetch all users\nasync function fetchUsers() {\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.employee_routes.GETALL_USERS, {\n        method: \"GET\",\n        credentials: \"include\"\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch users\");\n    const data = await res.json();\n    // Map id to string\n    return data.data.map((user)=>({\n            ...user,\n            id: String(user.id)\n        }));\n}\n// Fetch a single ticket by ID\nasync function fetchTicket(id) {\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.ticket_routes.GET_TICKET_BY_ID(id), {\n        method: \"GET\"\n    });\n    if (!res.ok) return null;\n    const data = await res.json();\n    const apiTicket = data.data;\n    // Map tags to UI format\n    const tags = (apiTicket.tags || []).map((tag)=>({\n            id: tag.id,\n            tagName: tag.tagName || tag.name,\n            name: tag.tagName || tag.name,\n            color: tag.color || \"bg-gray-100 text-gray-800\",\n            createdBy: tag.createdBy,\n            createdAt: tag.createdAt\n        }));\n    // Map dueDate from last stage if available\n    let dueDate = null;\n    if (Array.isArray(apiTicket.stages) && apiTicket.stages.length > 0) {\n        dueDate = apiTicket.stages[apiTicket.stages.length - 1].dueAt || null;\n    }\n    return {\n        id: apiTicket.id,\n        title: apiTicket.title || \"Ticket for Invoice \".concat(apiTicket.workItemId),\n        description: apiTicket.description || \"\",\n        status: apiTicket.status || \"todo\",\n        priority: (apiTicket.priority || \"low\").toLowerCase(),\n        dueDate: dueDate ? new Date(dueDate) : null,\n        createdAt: apiTicket.createdAt ? new Date(apiTicket.createdAt) : new Date(),\n        updatedAt: apiTicket.updatedAt ? new Date(apiTicket.updatedAt) : new Date(),\n        tags,\n        comments: apiTicket.comments || [],\n        pipeline: apiTicket.pipeline || {},\n        currentStage: apiTicket.currentStage || undefined,\n        currentStageId: apiTicket.currentStageId,\n        stages: apiTicket.stages || [],\n        createdBy: apiTicket.createdBy || \"\",\n        owner: apiTicket.owner || \"\"\n    };\n}\n// Update a ticket (status or other fields)\nasync function updateTicket(id, updates, username) {\n    const payload = {\n        ...updates,\n        ticketId: id,\n        ...username && {\n            updatedBy: username\n        }\n    };\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.ticket_routes.UPDATE_TICKET(id), {\n        method: \"PUT\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(payload)\n    });\n    if (!res.ok) throw new Error(\"Failed to update ticket\");\n    const data = await res.json();\n    return data.ticket || data.data;\n}\n// Update only the status of a ticket\nasync function updateTicketStatus(id, status, username) {\n    // The backend expects the full update body, so send only status if that's all that's changing\n    return updateTicket(id, {\n        status\n    }, username);\n}\n// Delete a ticket by ID\nasync function deleteTicket(id, username) {\n    const payload = username ? {\n        deletedBy: username\n    } : {};\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.ticket_routes.DELETE_TICKET(id), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(payload)\n    });\n    if (!res.ok) throw new Error(\"Failed to delete ticket\");\n}\n// Bulk delete tickets by looping over IDs\nasync function bulkDeleteTickets(ids, username) {\n    for (const id of ids){\n        await deleteTicket(id, username);\n    }\n}\n// Fetch all tags\nasync function fetchTags() {\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.tag_routes.GET_ALL_TAGS, {\n        method: \"GET\"\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch tags\");\n    const data = await res.json();\n    return data.data;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pms/manage_tickets/tickets.ts\n"));

/***/ })

});