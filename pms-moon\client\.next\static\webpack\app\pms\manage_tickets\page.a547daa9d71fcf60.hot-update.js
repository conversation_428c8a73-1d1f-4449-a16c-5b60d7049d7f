"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pms/manage_tickets/page",{

/***/ "(app-pages-browser)/./app/pms/manage_tickets/components/ticket-sidebar.tsx":
/*!**************************************************************!*\
  !*** ./app/pms/manage_tickets/components/ticket-sidebar.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TicketSidebar: function() { return /* binding */ TicketSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./components/ui/sheet.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _comment_section__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./comment-section */ \"(app-pages-browser)/./app/pms/manage_tickets/components/comment-section.tsx\");\n/* harmony import */ var _tag_manager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./tag-manager */ \"(app-pages-browser)/./app/pms/manage_tickets/components/tag-manager.tsx\");\n/* harmony import */ var _TicketContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../TicketContext */ \"(app-pages-browser)/./app/pms/manage_tickets/TicketContext.tsx\");\n/* harmony import */ var _activity_section__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./activity-section */ \"(app-pages-browser)/./app/pms/manage_tickets/components/activity-section.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ TicketSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TicketSidebar(param) {\n    let { ticket: initialTicket, isOpen, onClose, onOpenInNewTab, onTagsUpdated } = param;\n    var _ticket_pipeline, _ticket_pipeline1;\n    _s();\n    // All hooks must be called at the top, before any early returns\n    const [commentsCount, setCommentsCount] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(0);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(\"details\");\n    const { users, currentUser, setTickets, tickets } = (0,react__WEBPACK_IMPORTED_MODULE_7__.useContext)(_TicketContext__WEBPACK_IMPORTED_MODULE_10__.TicketContext);\n    const hasLoadedCommentsCount = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n    // Inline edit state for priority\n    const [editingPriority, setEditingPriority] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    var _initialTicket_priority;\n    const [priorityValue, setPriorityValue] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)((_initialTicket_priority = initialTicket === null || initialTicket === void 0 ? void 0 : initialTicket.priority) !== null && _initialTicket_priority !== void 0 ? _initialTicket_priority : \"low\");\n    const [priorityLoading, setPriorityLoading] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [priorityError, setPriorityError] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(\"\");\n    // Now, after all hooks, handle ticket logic\n    let ticket = tickets.find((t)=>t.id === (initialTicket === null || initialTicket === void 0 ? void 0 : initialTicket.id)) || initialTicket;\n    if (ticket && ticket.currentStage && ((_ticket_pipeline = ticket.pipeline) === null || _ticket_pipeline === void 0 ? void 0 : _ticket_pipeline.stages)) {\n        const pipelineStage = ticket.pipeline.stages.find((ps)=>ps.id === ticket.currentStage.pipelineStageId);\n        if (pipelineStage && !ticket.currentStage.name) {\n            ticket = {\n                ...ticket,\n                currentStage: {\n                    ...ticket.currentStage,\n                    name: pipelineStage.name\n                }\n            };\n        }\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        async function fetchCommentsCount() {\n            if (!ticket) return;\n            try {\n                var _data_data;\n                const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_6__.comment_routes.GET_COMMENTS_BY_TICKET(ticket.id));\n                const data = await res.json();\n                setCommentsCount(((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.length) || 0);\n            } catch (e) {\n                setCommentsCount(0);\n            }\n        }\n        if (isOpen && ticket && hasLoadedCommentsCount.current !== ticket.id) {\n            hasLoadedCommentsCount.current = ticket.id;\n            fetchCommentsCount();\n        }\n        if (!isOpen) {\n            hasLoadedCommentsCount.current = null;\n        }\n    }, [\n        isOpen,\n        ticket\n    ]);\n    if (!ticket) return null;\n    const priorityColors = {\n        low: \"bg-gray-100 text-gray-800\",\n        medium: \"bg-blue-100 text-blue-800\",\n        high: \"bg-orange-100 text-orange-800\",\n        urgent: \"bg-red-100 text-red-800\"\n    };\n    const currentStage = ticket.currentStage;\n    const assignedUser = currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedUser;\n    let assignedToDisplay;\n    if (currentUser && ((currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedTo) === currentUser.id || (currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedTo) === currentUser.username)) {\n        assignedToDisplay = \"You\";\n    } else if (assignedUser) {\n        assignedToDisplay = assignedUser.username || assignedUser.id;\n    } else {\n        assignedToDisplay = (currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedTo) || \"Unassigned\";\n    }\n    const badgeColors = [\n        \"bg-gray-200 text-gray-800\",\n        \"bg-blue-200 text-blue-800\",\n        \"bg-green-200 text-green-800\",\n        \"bg-yellow-200 text-yellow-800\",\n        \"bg-purple-200 text-purple-800\",\n        \"bg-pink-200 text-pink-800\",\n        \"bg-orange-200 text-orange-800\",\n        \"bg-red-200 text-red-800\"\n    ];\n    let stageColor = \"bg-gray-200 text-gray-800\";\n    if ((_ticket_pipeline1 = ticket.pipeline) === null || _ticket_pipeline1 === void 0 ? void 0 : _ticket_pipeline1.stages) {\n        const idx = ticket.pipeline.stages.findIndex((s)=>{\n            var _ticket_currentStage;\n            return s.id === ((_ticket_currentStage = ticket.currentStage) === null || _ticket_currentStage === void 0 ? void 0 : _ticket_currentStage.pipelineStageId);\n        });\n        if (idx !== -1) {\n            stageColor = badgeColors[idx % badgeColors.length];\n        }\n    }\n    const handleCommentAdded = ()=>{\n        setCommentsCount((prev)=>prev + 1);\n    };\n    const handleTagsUpdated = ()=>{\n        onTagsUpdated === null || onTagsUpdated === void 0 ? void 0 : onTagsUpdated();\n    };\n    // Update ticket API call\n    async function updateTicketField(field, value) {\n        setPriorityLoading(true);\n        setPriorityError(\"\");\n        try {\n            const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_6__.ticket_routes.UPDATE_TICKET(ticket.id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    ticketId: ticket.id,\n                    updatedBy: currentUser === null || currentUser === void 0 ? void 0 : currentUser.id,\n                    [field]: value\n                })\n            });\n            if (!res.ok) throw new Error(\"Failed to update ticket\");\n            setPriorityValue(value);\n            setTickets((prev)=>prev.map((t)=>t.id === ticket.id ? {\n                        ...t,\n                        priority: value\n                    } : t));\n            setEditingPriority(false);\n        } catch (e) {\n            setPriorityError(e.message || \"Error updating priority\");\n        } finally{\n            setPriorityLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.Sheet, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetContent, {\n            className: \"w-full sm:max-w-lg overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 pr-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetTitle, {\n                                        className: \"text-lg mb-2 leading-tight\",\n                                        children: ticket.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap items-center gap-2\",\n                                        children: [\n                                            editingPriority ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                                        value: priorityValue,\n                                                        onValueChange: (val)=>updateTicketField(\"priority\", val),\n                                                        disabled: priorityLoading,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                                className: priorityColors[priorityValue] + \" min-w-[100px]\",\n                                                                children: [\n                                                                    priorityLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"animate-spin h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 165,\n                                                                        columnNumber: 44\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"mr-1 h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 165,\n                                                                        columnNumber: 91\n                                                                    }, this),\n                                                                    priorityValue\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                        value: \"low\",\n                                                                        children: \"Low\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 169,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                        value: \"medium\",\n                                                                        children: \"Medium\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 170,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                        value: \"high\",\n                                                                        children: \"High\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                        value: \"urgent\",\n                                                                        children: \"Urgent\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 172,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    priorityError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-red-600 ml-2\",\n                                                        children: priorityError\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 39\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                className: priorityColors[priorityValue],\n                                                onClick: ()=>setEditingPriority(true),\n                                                style: {\n                                                    cursor: \"pointer\"\n                                                },\n                                                title: \"Click to edit\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"mr-1 h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    priorityValue\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentStage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                className: stageColor,\n                                                variant: \"secondary\",\n                                                children: currentStage.name || currentStage.pipelineStageId\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, this),\n                                            ticket.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                    className: \"\".concat(tag.color, \" text-xs\"),\n                                                    children: tag.tagName || tag.name\n                                                }, tag.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>onOpenInNewTab(ticket.id),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                        value: activeTab,\n                        onValueChange: setActiveTab,\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                className: \"grid w-full grid-cols-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"details\",\n                                        className: \"text-xs\",\n                                        children: \"Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"comments\",\n                                        className: \"text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Comments\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-1 text-blue-600 font-bold\",\n                                                children: [\n                                                    \"(\",\n                                                    commentsCount,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"tags\",\n                                        className: \"text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Tags\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"activity\",\n                                        className: \"text-xs\",\n                                        children: \"Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"details\",\n                                className: \"space-y-6 mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-3\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 text-sm leading-relaxed\",\n                                                children: ticket.description || \"No description provided\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                        children: \"Assigned To\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: assignedUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                                    className: \"h-5 w-5\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.AvatarImage, {\n                                                                            src: assignedUser.avatar || \" \",\n                                                                            alt: assignedToDisplay\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                            lineNumber: 236,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                                            className: \"text-xs\",\n                                                                            children: assignedUser.username ? assignedUser.username[0].toUpperCase() : \"\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                            lineNumber: 237,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: assignedToDisplay\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                    lineNumber: 241,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: assignedToDisplay\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentStage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                        children: \"Due Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm \".concat(currentStage.dueAt && new Date(currentStage.dueAt) < new Date() ? \"text-red-600\" : \"\"),\n                                                                children: currentStage.dueAt ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(currentStage.dueAt), \"PPP\") : \"No due date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                        children: \"Created\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(ticket.createdAt), \"PPP\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                        children: \"Last Updated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(ticket.updatedAt), \"PPP\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"comments\",\n                                className: \"space-y-4 mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_comment_section__WEBPACK_IMPORTED_MODULE_8__.CommentSection, {\n                                    ticketId: ticket.id,\n                                    createdBy: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.username) || \"\",\n                                    setCommentsCount: setCommentsCount,\n                                    onCommentsChange: (newComments)=>{\n                                        setTickets((prev)=>prev.map((t)=>t.id === ticket.id ? {\n                                                    ...t,\n                                                    comments: newComments\n                                                } : t));\n                                    },\n                                    isActive: activeTab === \"comments\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"tags\",\n                                className: \"space-y-4 mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tag_manager__WEBPACK_IMPORTED_MODULE_9__.TagManager, {\n                                    ticketId: ticket.id,\n                                    assignedTags: ticket.tags,\n                                    onTagsUpdated: handleTagsUpdated,\n                                    onTagsChange: (newTags)=>{\n                                        setTickets((prev)=>prev.map((t)=>t.id === ticket.id ? {\n                                                    ...t,\n                                                    tags: newTags\n                                                } : t));\n                                    },\n                                    createdBy: ticket.createdBy || \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"activity\",\n                                className: \"space-y-4 mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_activity_section__WEBPACK_IMPORTED_MODULE_11__.ActivitySection, {\n                                    ticketId: ticket.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n_s(TicketSidebar, \"kJn1Xh+J0Vgr+I6v1QlTJn7sRuU=\");\n_c = TicketSidebar;\nvar _c;\n$RefreshReg$(_c, \"TicketSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pms/manage_tickets/components/ticket-sidebar.tsx\n"));

/***/ })

});