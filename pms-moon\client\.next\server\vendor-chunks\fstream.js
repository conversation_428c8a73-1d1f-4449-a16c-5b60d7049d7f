/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fstream";
exports.ids = ["vendor-chunks/fstream"];
exports.modules = {

/***/ "(ssr)/./node_modules/fstream/fstream.js":
/*!*****************************************!*\
  !*** ./node_modules/fstream/fstream.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.Abstract = __webpack_require__(/*! ./lib/abstract.js */ \"(ssr)/./node_modules/fstream/lib/abstract.js\")\nexports.Reader = __webpack_require__(/*! ./lib/reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\nexports.Writer = __webpack_require__(/*! ./lib/writer.js */ \"(ssr)/./node_modules/fstream/lib/writer.js\")\n\nexports.File = {\n  Reader: __webpack_require__(/*! ./lib/file-reader.js */ \"(ssr)/./node_modules/fstream/lib/file-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/file-writer.js */ \"(ssr)/./node_modules/fstream/lib/file-writer.js\")\n}\n\nexports.Dir = {\n  Reader: __webpack_require__(/*! ./lib/dir-reader.js */ \"(ssr)/./node_modules/fstream/lib/dir-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/dir-writer.js */ \"(ssr)/./node_modules/fstream/lib/dir-writer.js\")\n}\n\nexports.Link = {\n  Reader: __webpack_require__(/*! ./lib/link-reader.js */ \"(ssr)/./node_modules/fstream/lib/link-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/link-writer.js */ \"(ssr)/./node_modules/fstream/lib/link-writer.js\")\n}\n\nexports.Proxy = {\n  Reader: __webpack_require__(/*! ./lib/proxy-reader.js */ \"(ssr)/./node_modules/fstream/lib/proxy-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/proxy-writer.js */ \"(ssr)/./node_modules/fstream/lib/proxy-writer.js\")\n}\n\nexports.Reader.Dir = exports.DirReader = exports.Dir.Reader\nexports.Reader.File = exports.FileReader = exports.File.Reader\nexports.Reader.Link = exports.LinkReader = exports.Link.Reader\nexports.Reader.Proxy = exports.ProxyReader = exports.Proxy.Reader\n\nexports.Writer.Dir = exports.DirWriter = exports.Dir.Writer\nexports.Writer.File = exports.FileWriter = exports.File.Writer\nexports.Writer.Link = exports.LinkWriter = exports.Link.Writer\nexports.Writer.Proxy = exports.ProxyWriter = exports.Proxy.Writer\n\nexports.collect = __webpack_require__(/*! ./lib/collect.js */ \"(ssr)/./node_modules/fstream/lib/collect.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/fstream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/abstract.js":
/*!**********************************************!*\
  !*** ./node_modules/fstream/lib/abstract.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// the parent class for all fstreams.\n\nmodule.exports = Abstract\n\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream)\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\n\nfunction Abstract () {\n  Stream.call(this)\n}\n\ninherits(Abstract, Stream)\n\nAbstract.prototype.on = function (ev, fn) {\n  if (ev === 'ready' && this.ready) {\n    process.nextTick(fn.bind(this))\n  } else {\n    Stream.prototype.on.call(this, ev, fn)\n  }\n  return this\n}\n\nAbstract.prototype.abort = function () {\n  this._aborted = true\n  this.emit('abort')\n}\n\nAbstract.prototype.destroy = function () {}\n\nAbstract.prototype.warn = function (msg, code) {\n  var self = this\n  var er = decorate(msg, code, self)\n  if (!self.listeners('warn')) {\n    console.error('%s %s\\n' +\n    'path = %s\\n' +\n    'syscall = %s\\n' +\n    'fstream_type = %s\\n' +\n    'fstream_path = %s\\n' +\n    'fstream_unc_path = %s\\n' +\n    'fstream_class = %s\\n' +\n    'fstream_stack =\\n%s\\n',\n      code || 'UNKNOWN',\n      er.stack,\n      er.path,\n      er.syscall,\n      er.fstream_type,\n      er.fstream_path,\n      er.fstream_unc_path,\n      er.fstream_class,\n      er.fstream_stack.join('\\n'))\n  } else {\n    self.emit('warn', er)\n  }\n}\n\nAbstract.prototype.info = function (msg, code) {\n  this.emit('info', msg, code)\n}\n\nAbstract.prototype.error = function (msg, code, th) {\n  var er = decorate(msg, code, this)\n  if (th) throw er\n  else this.emit('error', er)\n}\n\nfunction decorate (er, code, self) {\n  if (!(er instanceof Error)) er = new Error(er)\n  er.code = er.code || code\n  er.path = er.path || self.path\n  er.fstream_type = er.fstream_type || self.type\n  er.fstream_path = er.fstream_path || self.path\n  if (self._path !== self.path) {\n    er.fstream_unc_path = er.fstream_unc_path || self._path\n  }\n  if (self.linkpath) {\n    er.fstream_linkpath = er.fstream_linkpath || self.linkpath\n  }\n  er.fstream_class = er.fstream_class || self.constructor.name\n  er.fstream_stack = er.fstream_stack ||\n    new Error().stack.split(/\\n/).slice(3).map(function (s) {\n      return s.replace(/^ {4}at /, '')\n    })\n\n  return er\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/abstract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/collect.js":
/*!*********************************************!*\
  !*** ./node_modules/fstream/lib/collect.js ***!
  \*********************************************/
/***/ ((module) => {

eval("module.exports = collect\n\nfunction collect (stream) {\n  if (stream._collected) return\n\n  if (stream._paused) return stream.on('resume', collect.bind(null, stream))\n\n  stream._collected = true\n  stream.pause()\n\n  stream.on('data', save)\n  stream.on('end', save)\n  var buf = []\n  function save (b) {\n    if (typeof b === 'string') b = new Buffer(b)\n    if (Buffer.isBuffer(b) && !b.length) return\n    buf.push(b)\n  }\n\n  stream.on('entry', saveEntry)\n  var entryBuffer = []\n  function saveEntry (e) {\n    collect(e)\n    entryBuffer.push(e)\n  }\n\n  stream.on('proxy', proxyPause)\n  function proxyPause (p) {\n    p.pause()\n  }\n\n  // replace the pipe method with a new version that will\n  // unlock the buffered stuff.  if you just call .pipe()\n  // without a destination, then it'll re-play the events.\n  stream.pipe = (function (orig) {\n    return function (dest) {\n      // console.error(' === open the pipes', dest && dest.path)\n\n      // let the entries flow through one at a time.\n      // Once they're all done, then we can resume completely.\n      var e = 0\n      ;(function unblockEntry () {\n        var entry = entryBuffer[e++]\n        // console.error(\" ==== unblock entry\", entry && entry.path)\n        if (!entry) return resume()\n        entry.on('end', unblockEntry)\n        if (dest) dest.add(entry)\n        else stream.emit('entry', entry)\n      })()\n\n      function resume () {\n        stream.removeListener('entry', saveEntry)\n        stream.removeListener('data', save)\n        stream.removeListener('end', save)\n\n        stream.pipe = orig\n        if (dest) stream.pipe(dest)\n\n        buf.forEach(function (b) {\n          if (b) stream.emit('data', b)\n          else stream.emit('end')\n        })\n\n        stream.resume()\n      }\n\n      return dest\n    }\n  })(stream.pipe)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/collect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/dir-reader.js":
/*!************************************************!*\
  !*** ./node_modules/fstream/lib/dir-reader.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// A thing that emits \"entry\" events with Reader objects\n// Pausing it causes it to stop emitting entry events, and also\n// pauses the current entry if there is one.\n\nmodule.exports = DirReader\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\nvar assert = (__webpack_require__(/*! assert */ \"assert\").ok)\n\ninherits(DirReader, Reader)\n\nfunction DirReader (props) {\n  var self = this\n  if (!(self instanceof DirReader)) {\n    throw new Error('DirReader must be called as constructor.')\n  }\n\n  // should already be established as a Directory type\n  if (props.type !== 'Directory' || !props.Directory) {\n    throw new Error('Non-directory type ' + props.type)\n  }\n\n  self.entries = null\n  self._index = -1\n  self._paused = false\n  self._length = -1\n\n  if (props.sort) {\n    this.sort = props.sort\n  }\n\n  Reader.call(this, props)\n}\n\nDirReader.prototype._getEntries = function () {\n  var self = this\n\n  // race condition.  might pause() before calling _getEntries,\n  // and then resume, and try to get them a second time.\n  if (self._gotEntries) return\n  self._gotEntries = true\n\n  fs.readdir(self._path, function (er, entries) {\n    if (er) return self.error(er)\n\n    self.entries = entries\n\n    self.emit('entries', entries)\n    if (self._paused) self.once('resume', processEntries)\n    else processEntries()\n\n    function processEntries () {\n      self._length = self.entries.length\n      if (typeof self.sort === 'function') {\n        self.entries = self.entries.sort(self.sort.bind(self))\n      }\n      self._read()\n    }\n  })\n}\n\n// start walking the dir, and emit an \"entry\" event for each one.\nDirReader.prototype._read = function () {\n  var self = this\n\n  if (!self.entries) return self._getEntries()\n\n  if (self._paused || self._currentEntry || self._aborted) {\n    // console.error('DR paused=%j, current=%j, aborted=%j', self._paused, !!self._currentEntry, self._aborted)\n    return\n  }\n\n  self._index++\n  if (self._index >= self.entries.length) {\n    if (!self._ended) {\n      self._ended = true\n      self.emit('end')\n      self.emit('close')\n    }\n    return\n  }\n\n  // ok, handle this one, then.\n\n  // save creating a proxy, by stat'ing the thing now.\n  var p = path.resolve(self._path, self.entries[self._index])\n  assert(p !== self._path)\n  assert(self.entries[self._index])\n\n  // set this to prevent trying to _read() again in the stat time.\n  self._currentEntry = p\n  fs[ self.props.follow ? 'stat' : 'lstat' ](p, function (er, stat) {\n    if (er) return self.error(er)\n\n    var who = self._proxy || self\n\n    stat.path = p\n    stat.basename = path.basename(p)\n    stat.dirname = path.dirname(p)\n    var childProps = self.getChildProps.call(who, stat)\n    childProps.path = p\n    childProps.basename = path.basename(p)\n    childProps.dirname = path.dirname(p)\n\n    var entry = Reader(childProps, stat)\n\n    // console.error(\"DR Entry\", p, stat.size)\n\n    self._currentEntry = entry\n\n    // \"entry\" events are for direct entries in a specific dir.\n    // \"child\" events are for any and all children at all levels.\n    // This nomenclature is not completely final.\n\n    entry.on('pause', function (who) {\n      if (!self._paused && !entry._disowned) {\n        self.pause(who)\n      }\n    })\n\n    entry.on('resume', function (who) {\n      if (self._paused && !entry._disowned) {\n        self.resume(who)\n      }\n    })\n\n    entry.on('stat', function (props) {\n      self.emit('_entryStat', entry, props)\n      if (entry._aborted) return\n      if (entry._paused) {\n        entry.once('resume', function () {\n          self.emit('entryStat', entry, props)\n        })\n      } else self.emit('entryStat', entry, props)\n    })\n\n    entry.on('ready', function EMITCHILD () {\n      // console.error(\"DR emit child\", entry._path)\n      if (self._paused) {\n        // console.error(\"  DR emit child - try again later\")\n        // pause the child, and emit the \"entry\" event once we drain.\n        // console.error(\"DR pausing child entry\")\n        entry.pause(self)\n        return self.once('resume', EMITCHILD)\n      }\n\n      // skip over sockets.  they can't be piped around properly,\n      // so there's really no sense even acknowledging them.\n      // if someone really wants to see them, they can listen to\n      // the \"socket\" events.\n      if (entry.type === 'Socket') {\n        self.emit('socket', entry)\n      } else {\n        self.emitEntry(entry)\n      }\n    })\n\n    var ended = false\n    entry.on('close', onend)\n    entry.on('disown', onend)\n    function onend () {\n      if (ended) return\n      ended = true\n      self.emit('childEnd', entry)\n      self.emit('entryEnd', entry)\n      self._currentEntry = null\n      if (!self._paused) {\n        self._read()\n      }\n    }\n\n    // XXX Remove this.  Works in node as of 0.6.2 or so.\n    // Long filenames should not break stuff.\n    entry.on('error', function (er) {\n      if (entry._swallowErrors) {\n        self.warn(er)\n        entry.emit('end')\n        entry.emit('close')\n      } else {\n        self.emit('error', er)\n      }\n    })\n\n    // proxy up some events.\n    ;[\n      'child',\n      'childEnd',\n      'warn'\n    ].forEach(function (ev) {\n      entry.on(ev, self.emit.bind(self, ev))\n    })\n  })\n}\n\nDirReader.prototype.disown = function (entry) {\n  entry.emit('beforeDisown')\n  entry._disowned = true\n  entry.parent = entry.root = null\n  if (entry === this._currentEntry) {\n    this._currentEntry = null\n  }\n  entry.emit('disown')\n}\n\nDirReader.prototype.getChildProps = function () {\n  return {\n    depth: this.depth + 1,\n    root: this.root || this,\n    parent: this,\n    follow: this.follow,\n    filter: this.filter,\n    sort: this.props.sort,\n    hardlinks: this.props.hardlinks\n  }\n}\n\nDirReader.prototype.pause = function (who) {\n  var self = this\n  if (self._paused) return\n  who = who || self\n  self._paused = true\n  if (self._currentEntry && self._currentEntry.pause) {\n    self._currentEntry.pause(who)\n  }\n  self.emit('pause', who)\n}\n\nDirReader.prototype.resume = function (who) {\n  var self = this\n  if (!self._paused) return\n  who = who || self\n\n  self._paused = false\n  // console.error('DR Emit Resume', self._path)\n  self.emit('resume', who)\n  if (self._paused) {\n    // console.error('DR Re-paused', self._path)\n    return\n  }\n\n  if (self._currentEntry) {\n    if (self._currentEntry.resume) self._currentEntry.resume(who)\n  } else self._read()\n}\n\nDirReader.prototype.emitEntry = function (entry) {\n  this.emit('entry', entry)\n  this.emit('child', entry)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/dir-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/dir-writer.js":
/*!************************************************!*\
  !*** ./node_modules/fstream/lib/dir-writer.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// It is expected that, when .add() returns false, the consumer\n// of the DirWriter will pause until a \"drain\" event occurs. Note\n// that this is *almost always going to be the case*, unless the\n// thing being written is some sort of unsupported type, and thus\n// skipped over.\n\nmodule.exports = DirWriter\n\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(ssr)/./node_modules/fstream/lib/writer.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar mkdir = __webpack_require__(/*! mkdirp */ \"(ssr)/./node_modules/mkdirp/index.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar collect = __webpack_require__(/*! ./collect.js */ \"(ssr)/./node_modules/fstream/lib/collect.js\")\n\ninherits(DirWriter, Writer)\n\nfunction DirWriter (props) {\n  var self = this\n  if (!(self instanceof DirWriter)) {\n    self.error('DirWriter must be called as constructor.', null, true)\n  }\n\n  // should already be established as a Directory type\n  if (props.type !== 'Directory' || !props.Directory) {\n    self.error('Non-directory type ' + props.type + ' ' +\n      JSON.stringify(props), null, true)\n  }\n\n  Writer.call(this, props)\n}\n\nDirWriter.prototype._create = function () {\n  var self = this\n  mkdir(self._path, Writer.dirmode, function (er) {\n    if (er) return self.error(er)\n    // ready to start getting entries!\n    self.ready = true\n    self.emit('ready')\n    self._process()\n  })\n}\n\n// a DirWriter has an add(entry) method, but its .write() doesn't\n// do anything.  Why a no-op rather than a throw?  Because this\n// leaves open the door for writing directory metadata for\n// gnu/solaris style dumpdirs.\nDirWriter.prototype.write = function () {\n  return true\n}\n\nDirWriter.prototype.end = function () {\n  this._ended = true\n  this._process()\n}\n\nDirWriter.prototype.add = function (entry) {\n  var self = this\n\n  // console.error('\\tadd', entry._path, '->', self._path)\n  collect(entry)\n  if (!self.ready || self._currentEntry) {\n    self._buffer.push(entry)\n    return false\n  }\n\n  // create a new writer, and pipe the incoming entry into it.\n  if (self._ended) {\n    return self.error('add after end')\n  }\n\n  self._buffer.push(entry)\n  self._process()\n\n  return this._buffer.length === 0\n}\n\nDirWriter.prototype._process = function () {\n  var self = this\n\n  // console.error('DW Process p=%j', self._processing, self.basename)\n\n  if (self._processing) return\n\n  var entry = self._buffer.shift()\n  if (!entry) {\n    // console.error(\"DW Drain\")\n    self.emit('drain')\n    if (self._ended) self._finish()\n    return\n  }\n\n  self._processing = true\n  // console.error(\"DW Entry\", entry._path)\n\n  self.emit('entry', entry)\n\n  // ok, add this entry\n  //\n  // don't allow recursive copying\n  var p = entry\n  var pp\n  do {\n    pp = p._path || p.path\n    if (pp === self.root._path || pp === self._path ||\n      (pp && pp.indexOf(self._path) === 0)) {\n      // console.error('DW Exit (recursive)', entry.basename, self._path)\n      self._processing = false\n      if (entry._collected) entry.pipe()\n      return self._process()\n    }\n    p = p.parent\n  } while (p)\n\n  // console.error(\"DW not recursive\")\n\n  // chop off the entry's root dir, replace with ours\n  var props = {\n    parent: self,\n    root: self.root || self,\n    type: entry.type,\n    depth: self.depth + 1\n  }\n\n  pp = entry._path || entry.path || entry.props.path\n  if (entry.parent) {\n    pp = pp.substr(entry.parent._path.length + 1)\n  }\n  // get rid of any ../../ shenanigans\n  props.path = path.join(self.path, path.join('/', pp))\n\n  // if i have a filter, the child should inherit it.\n  props.filter = self.filter\n\n  // all the rest of the stuff, copy over from the source.\n  Object.keys(entry.props).forEach(function (k) {\n    if (!props.hasOwnProperty(k)) {\n      props[k] = entry.props[k]\n    }\n  })\n\n  // not sure at this point what kind of writer this is.\n  var child = self._currentChild = new Writer(props)\n  child.on('ready', function () {\n    // console.error(\"DW Child Ready\", child.type, child._path)\n    // console.error(\"  resuming\", entry._path)\n    entry.pipe(child)\n    entry.resume()\n  })\n\n  // XXX Make this work in node.\n  // Long filenames should not break stuff.\n  child.on('error', function (er) {\n    if (child._swallowErrors) {\n      self.warn(er)\n      child.emit('end')\n      child.emit('close')\n    } else {\n      self.emit('error', er)\n    }\n  })\n\n  // we fire _end internally *after* end, so that we don't move on\n  // until any \"end\" listeners have had their chance to do stuff.\n  child.on('close', onend)\n  var ended = false\n  function onend () {\n    if (ended) return\n    ended = true\n    // console.error(\"* DW Child end\", child.basename)\n    self._currentChild = null\n    self._processing = false\n    self._process()\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/dir-writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/file-reader.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/file-reader.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Basically just a wrapper around an fs.ReadStream\n\nmodule.exports = FileReader\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\nvar EOF = {EOF: true}\nvar CLOSE = {CLOSE: true}\n\ninherits(FileReader, Reader)\n\nfunction FileReader (props) {\n  // console.error(\"    FR create\", props.path, props.size, new Error().stack)\n  var self = this\n  if (!(self instanceof FileReader)) {\n    throw new Error('FileReader must be called as constructor.')\n  }\n\n  // should already be established as a File type\n  // XXX Todo: preserve hardlinks by tracking dev+inode+nlink,\n  // with a HardLinkReader class.\n  if (!((props.type === 'Link' && props.Link) ||\n    (props.type === 'File' && props.File))) {\n    throw new Error('Non-file type ' + props.type)\n  }\n\n  self._buffer = []\n  self._bytesEmitted = 0\n  Reader.call(self, props)\n}\n\nFileReader.prototype._getStream = function () {\n  var self = this\n  var stream = self._stream = fs.createReadStream(self._path, self.props)\n\n  if (self.props.blksize) {\n    stream.bufferSize = self.props.blksize\n  }\n\n  stream.on('open', self.emit.bind(self, 'open'))\n\n  stream.on('data', function (c) {\n    // console.error('\\t\\t%d %s', c.length, self.basename)\n    self._bytesEmitted += c.length\n    // no point saving empty chunks\n    if (!c.length) {\n      return\n    } else if (self._paused || self._buffer.length) {\n      self._buffer.push(c)\n      self._read()\n    } else self.emit('data', c)\n  })\n\n  stream.on('end', function () {\n    if (self._paused || self._buffer.length) {\n      // console.error('FR Buffering End', self._path)\n      self._buffer.push(EOF)\n      self._read()\n    } else {\n      self.emit('end')\n    }\n\n    if (self._bytesEmitted !== self.props.size) {\n      self.error(\"Didn't get expected byte count\\n\" +\n        'expect: ' + self.props.size + '\\n' +\n        'actual: ' + self._bytesEmitted)\n    }\n  })\n\n  stream.on('close', function () {\n    if (self._paused || self._buffer.length) {\n      // console.error('FR Buffering Close', self._path)\n      self._buffer.push(CLOSE)\n      self._read()\n    } else {\n      // console.error('FR close 1', self._path)\n      self.emit('close')\n    }\n  })\n\n  stream.on('error', function (e) {\n    self.emit('error', e)\n  })\n\n  self._read()\n}\n\nFileReader.prototype._read = function () {\n  var self = this\n  // console.error('FR _read', self._path)\n  if (self._paused) {\n    // console.error('FR _read paused', self._path)\n    return\n  }\n\n  if (!self._stream) {\n    // console.error('FR _getStream calling', self._path)\n    return self._getStream()\n  }\n\n  // clear out the buffer, if there is one.\n  if (self._buffer.length) {\n    // console.error('FR _read has buffer', self._buffer.length, self._path)\n    var buf = self._buffer\n    for (var i = 0, l = buf.length; i < l; i++) {\n      var c = buf[i]\n      if (c === EOF) {\n        // console.error('FR Read emitting buffered end', self._path)\n        self.emit('end')\n      } else if (c === CLOSE) {\n        // console.error('FR Read emitting buffered close', self._path)\n        self.emit('close')\n      } else {\n        // console.error('FR Read emitting buffered data', self._path)\n        self.emit('data', c)\n      }\n\n      if (self._paused) {\n        // console.error('FR Read Re-pausing at '+i, self._path)\n        self._buffer = buf.slice(i)\n        return\n      }\n    }\n    self._buffer.length = 0\n  }\n// console.error(\"FR _read done\")\n// that's about all there is to it.\n}\n\nFileReader.prototype.pause = function (who) {\n  var self = this\n  // console.error('FR Pause', self._path)\n  if (self._paused) return\n  who = who || self\n  self._paused = true\n  if (self._stream) self._stream.pause()\n  self.emit('pause', who)\n}\n\nFileReader.prototype.resume = function (who) {\n  var self = this\n  // console.error('FR Resume', self._path)\n  if (!self._paused) return\n  who = who || self\n  self.emit('resume', who)\n  self._paused = false\n  if (self._stream) self._stream.resume()\n  self._read()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/file-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/file-writer.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/file-writer.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = FileWriter\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(ssr)/./node_modules/fstream/lib/writer.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar EOF = {}\n\ninherits(FileWriter, Writer)\n\nfunction FileWriter (props) {\n  var self = this\n  if (!(self instanceof FileWriter)) {\n    throw new Error('FileWriter must be called as constructor.')\n  }\n\n  // should already be established as a File type\n  if (props.type !== 'File' || !props.File) {\n    throw new Error('Non-file type ' + props.type)\n  }\n\n  self._buffer = []\n  self._bytesWritten = 0\n\n  Writer.call(this, props)\n}\n\nFileWriter.prototype._create = function () {\n  var self = this\n  if (self._stream) return\n\n  var so = {}\n  if (self.props.flags) so.flags = self.props.flags\n  so.mode = Writer.filemode\n  if (self._old && self._old.blksize) so.bufferSize = self._old.blksize\n\n  self._stream = fs.createWriteStream(self._path, so)\n\n  self._stream.on('open', function () {\n    // console.error(\"FW open\", self._buffer, self._path)\n    self.ready = true\n    self._buffer.forEach(function (c) {\n      if (c === EOF) self._stream.end()\n      else self._stream.write(c)\n    })\n    self.emit('ready')\n    // give this a kick just in case it needs it.\n    self.emit('drain')\n  })\n\n  self._stream.on('error', function (er) { self.emit('error', er) })\n\n  self._stream.on('drain', function () { self.emit('drain') })\n\n  self._stream.on('close', function () {\n    // console.error('\\n\\nFW Stream Close', self._path, self.size)\n    self._finish()\n  })\n}\n\nFileWriter.prototype.write = function (c) {\n  var self = this\n\n  self._bytesWritten += c.length\n\n  if (!self.ready) {\n    if (!Buffer.isBuffer(c) && typeof c !== 'string') {\n      throw new Error('invalid write data')\n    }\n    self._buffer.push(c)\n    return false\n  }\n\n  var ret = self._stream.write(c)\n  // console.error('\\t-- fw wrote, _stream says', ret, self._stream._queue.length)\n\n  // allow 2 buffered writes, because otherwise there's just too\n  // much stop and go bs.\n  if (ret === false && self._stream._queue) {\n    return self._stream._queue.length <= 2\n  } else {\n    return ret\n  }\n}\n\nFileWriter.prototype.end = function (c) {\n  var self = this\n\n  if (c) self.write(c)\n\n  if (!self.ready) {\n    self._buffer.push(EOF)\n    return false\n  }\n\n  return self._stream.end()\n}\n\nFileWriter.prototype._finish = function () {\n  var self = this\n  if (typeof self.size === 'number' && self._bytesWritten !== self.size) {\n    self.error(\n      'Did not get expected byte count.\\n' +\n      'expect: ' + self.size + '\\n' +\n      'actual: ' + self._bytesWritten)\n  }\n  Writer.prototype._finish.call(self)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/file-writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/get-type.js":
/*!**********************************************!*\
  !*** ./node_modules/fstream/lib/get-type.js ***!
  \**********************************************/
/***/ ((module) => {

eval("module.exports = getType\n\nfunction getType (st) {\n  var types = [\n    'Directory',\n    'File',\n    'SymbolicLink',\n    'Link', // special for hardlinks from tarballs\n    'BlockDevice',\n    'CharacterDevice',\n    'FIFO',\n    'Socket'\n  ]\n  var type\n\n  if (st.type && types.indexOf(st.type) !== -1) {\n    st[st.type] = true\n    return st.type\n  }\n\n  for (var i = 0, l = types.length; i < l; i++) {\n    type = types[i]\n    var is = st[type] || st['is' + type]\n    if (typeof is === 'function') is = is.call(st)\n    if (is) {\n      st[type] = true\n      st.type = type\n      return type\n    }\n  }\n\n  return null\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvZ2V0LXR5cGUuanMiLCJtYXBwaW5ncyI6IkFBQUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG9DQUFvQyxPQUFPO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2xpZW50Ly4vbm9kZV9tb2R1bGVzL2ZzdHJlYW0vbGliL2dldC10eXBlLmpzPzk4YWYiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBnZXRUeXBlXG5cbmZ1bmN0aW9uIGdldFR5cGUgKHN0KSB7XG4gIHZhciB0eXBlcyA9IFtcbiAgICAnRGlyZWN0b3J5JyxcbiAgICAnRmlsZScsXG4gICAgJ1N5bWJvbGljTGluaycsXG4gICAgJ0xpbmsnLCAvLyBzcGVjaWFsIGZvciBoYXJkbGlua3MgZnJvbSB0YXJiYWxsc1xuICAgICdCbG9ja0RldmljZScsXG4gICAgJ0NoYXJhY3RlckRldmljZScsXG4gICAgJ0ZJRk8nLFxuICAgICdTb2NrZXQnXG4gIF1cbiAgdmFyIHR5cGVcblxuICBpZiAoc3QudHlwZSAmJiB0eXBlcy5pbmRleE9mKHN0LnR5cGUpICE9PSAtMSkge1xuICAgIHN0W3N0LnR5cGVdID0gdHJ1ZVxuICAgIHJldHVybiBzdC50eXBlXG4gIH1cblxuICBmb3IgKHZhciBpID0gMCwgbCA9IHR5cGVzLmxlbmd0aDsgaSA8IGw7IGkrKykge1xuICAgIHR5cGUgPSB0eXBlc1tpXVxuICAgIHZhciBpcyA9IHN0W3R5cGVdIHx8IHN0WydpcycgKyB0eXBlXVxuICAgIGlmICh0eXBlb2YgaXMgPT09ICdmdW5jdGlvbicpIGlzID0gaXMuY2FsbChzdClcbiAgICBpZiAoaXMpIHtcbiAgICAgIHN0W3R5cGVdID0gdHJ1ZVxuICAgICAgc3QudHlwZSA9IHR5cGVcbiAgICAgIHJldHVybiB0eXBlXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIG51bGxcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/get-type.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/link-reader.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/link-reader.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Basically just a wrapper around an fs.readlink\n//\n// XXX: Enhance this to support the Link type, by keeping\n// a lookup table of {<dev+inode>:<path>}, so that hardlinks\n// can be preserved in tarballs.\n\nmodule.exports = LinkReader\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\n\ninherits(LinkReader, Reader)\n\nfunction LinkReader (props) {\n  var self = this\n  if (!(self instanceof LinkReader)) {\n    throw new Error('LinkReader must be called as constructor.')\n  }\n\n  if (!((props.type === 'Link' && props.Link) ||\n    (props.type === 'SymbolicLink' && props.SymbolicLink))) {\n    throw new Error('Non-link type ' + props.type)\n  }\n\n  Reader.call(self, props)\n}\n\n// When piping a LinkReader into a LinkWriter, we have to\n// already have the linkpath property set, so that has to\n// happen *before* the \"ready\" event, which means we need to\n// override the _stat method.\nLinkReader.prototype._stat = function (currentStat) {\n  var self = this\n  fs.readlink(self._path, function (er, linkpath) {\n    if (er) return self.error(er)\n    self.linkpath = self.props.linkpath = linkpath\n    self.emit('linkpath', linkpath)\n    Reader.prototype._stat.call(self, currentStat)\n  })\n}\n\nLinkReader.prototype._read = function () {\n  var self = this\n  if (self._paused) return\n  // basically just a no-op, since we got all the info we need\n  // from the _stat method\n  if (!self._ended) {\n    self.emit('end')\n    self.emit('close')\n    self._ended = true\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/link-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/link-writer.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/link-writer.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = LinkWriter\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(ssr)/./node_modules/fstream/lib/writer.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar rimraf = __webpack_require__(/*! rimraf */ \"rimraf\")\n\ninherits(LinkWriter, Writer)\n\nfunction LinkWriter (props) {\n  var self = this\n  if (!(self instanceof LinkWriter)) {\n    throw new Error('LinkWriter must be called as constructor.')\n  }\n\n  // should already be established as a Link type\n  if (!((props.type === 'Link' && props.Link) ||\n    (props.type === 'SymbolicLink' && props.SymbolicLink))) {\n    throw new Error('Non-link type ' + props.type)\n  }\n\n  if (props.linkpath === '') props.linkpath = '.'\n  if (!props.linkpath) {\n    self.error('Need linkpath property to create ' + props.type)\n  }\n\n  Writer.call(this, props)\n}\n\nLinkWriter.prototype._create = function () {\n  // console.error(\" LW _create\")\n  var self = this\n  var hard = self.type === 'Link' || process.platform === 'win32'\n  var link = hard ? 'link' : 'symlink'\n  var lp = hard ? path.resolve(self.dirname, self.linkpath) : self.linkpath\n\n  // can only change the link path by clobbering\n  // For hard links, let's just assume that's always the case, since\n  // there's no good way to read them if we don't already know.\n  if (hard) return clobber(self, lp, link)\n\n  fs.readlink(self._path, function (er, p) {\n    // only skip creation if it's exactly the same link\n    if (p && p === lp) return finish(self)\n    clobber(self, lp, link)\n  })\n}\n\nfunction clobber (self, lp, link) {\n  rimraf(self._path, function (er) {\n    if (er) return self.error(er)\n    create(self, lp, link)\n  })\n}\n\nfunction create (self, lp, link) {\n  fs[link](lp, self._path, function (er) {\n    // if this is a hard link, and we're in the process of writing out a\n    // directory, it's very possible that the thing we're linking to\n    // doesn't exist yet (especially if it was intended as a symlink),\n    // so swallow ENOENT errors here and just soldier in.\n    // Additionally, an EPERM or EACCES can happen on win32 if it's trying\n    // to make a link to a directory.  Again, just skip it.\n    // A better solution would be to have fs.symlink be supported on\n    // windows in some nice fashion.\n    if (er) {\n      if ((er.code === 'ENOENT' ||\n        er.code === 'EACCES' ||\n        er.code === 'EPERM') && process.platform === 'win32') {\n        self.ready = true\n        self.emit('ready')\n        self.emit('end')\n        self.emit('close')\n        self.end = self._finish = function () {}\n      } else return self.error(er)\n    }\n    finish(self)\n  })\n}\n\nfunction finish (self) {\n  self.ready = true\n  self.emit('ready')\n  if (self._ended && !self._finished) self._finish()\n}\n\nLinkWriter.prototype.end = function () {\n  // console.error(\"LW finish in end\")\n  this._ended = true\n  if (this.ready) {\n    this._finished = true\n    this._finish()\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/link-writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/proxy-reader.js":
/*!**************************************************!*\
  !*** ./node_modules/fstream/lib/proxy-reader.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// A reader for when we don't yet know what kind of thing\n// the thing is.\n\nmodule.exports = ProxyReader\n\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(ssr)/./node_modules/fstream/lib/get-type.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\n\ninherits(ProxyReader, Reader)\n\nfunction ProxyReader (props) {\n  var self = this\n  if (!(self instanceof ProxyReader)) {\n    throw new Error('ProxyReader must be called as constructor.')\n  }\n\n  self.props = props\n  self._buffer = []\n  self.ready = false\n\n  Reader.call(self, props)\n}\n\nProxyReader.prototype._stat = function () {\n  var self = this\n  var props = self.props\n  // stat the thing to see what the proxy should be.\n  var stat = props.follow ? 'stat' : 'lstat'\n\n  fs[stat](props.path, function (er, current) {\n    var type\n    if (er || !current) {\n      type = 'File'\n    } else {\n      type = getType(current)\n    }\n\n    props[type] = true\n    props.type = self.type = type\n\n    self._old = current\n    self._addProxy(Reader(props, current))\n  })\n}\n\nProxyReader.prototype._addProxy = function (proxy) {\n  var self = this\n  if (self._proxyTarget) {\n    return self.error('proxy already set')\n  }\n\n  self._proxyTarget = proxy\n  proxy._proxy = self\n\n  ;[\n    'error',\n    'data',\n    'end',\n    'close',\n    'linkpath',\n    'entry',\n    'entryEnd',\n    'child',\n    'childEnd',\n    'warn',\n    'stat'\n  ].forEach(function (ev) {\n    // console.error('~~ proxy event', ev, self.path)\n    proxy.on(ev, self.emit.bind(self, ev))\n  })\n\n  self.emit('proxy', proxy)\n\n  proxy.on('ready', function () {\n    // console.error(\"~~ proxy is ready!\", self.path)\n    self.ready = true\n    self.emit('ready')\n  })\n\n  var calls = self._buffer\n  self._buffer.length = 0\n  calls.forEach(function (c) {\n    proxy[c[0]].apply(proxy, c[1])\n  })\n}\n\nProxyReader.prototype.pause = function () {\n  return this._proxyTarget ? this._proxyTarget.pause() : false\n}\n\nProxyReader.prototype.resume = function () {\n  return this._proxyTarget ? this._proxyTarget.resume() : false\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/proxy-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/proxy-writer.js":
/*!**************************************************!*\
  !*** ./node_modules/fstream/lib/proxy-writer.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// A writer for when we don't know what kind of thing\n// the thing is.  That is, it's not explicitly set,\n// so we're going to make it whatever the thing already\n// is, or \"File\"\n//\n// Until then, collect all events.\n\nmodule.exports = ProxyWriter\n\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(ssr)/./node_modules/fstream/lib/writer.js\")\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(ssr)/./node_modules/fstream/lib/get-type.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar collect = __webpack_require__(/*! ./collect.js */ \"(ssr)/./node_modules/fstream/lib/collect.js\")\nvar fs = __webpack_require__(/*! fs */ \"fs\")\n\ninherits(ProxyWriter, Writer)\n\nfunction ProxyWriter (props) {\n  var self = this\n  if (!(self instanceof ProxyWriter)) {\n    throw new Error('ProxyWriter must be called as constructor.')\n  }\n\n  self.props = props\n  self._needDrain = false\n\n  Writer.call(self, props)\n}\n\nProxyWriter.prototype._stat = function () {\n  var self = this\n  var props = self.props\n  // stat the thing to see what the proxy should be.\n  var stat = props.follow ? 'stat' : 'lstat'\n\n  fs[stat](props.path, function (er, current) {\n    var type\n    if (er || !current) {\n      type = 'File'\n    } else {\n      type = getType(current)\n    }\n\n    props[type] = true\n    props.type = self.type = type\n\n    self._old = current\n    self._addProxy(Writer(props, current))\n  })\n}\n\nProxyWriter.prototype._addProxy = function (proxy) {\n  // console.error(\"~~ set proxy\", this.path)\n  var self = this\n  if (self._proxy) {\n    return self.error('proxy already set')\n  }\n\n  self._proxy = proxy\n  ;[\n    'ready',\n    'error',\n    'close',\n    'pipe',\n    'drain',\n    'warn'\n  ].forEach(function (ev) {\n    proxy.on(ev, self.emit.bind(self, ev))\n  })\n\n  self.emit('proxy', proxy)\n\n  var calls = self._buffer\n  calls.forEach(function (c) {\n    // console.error(\"~~ ~~ proxy buffered call\", c[0], c[1])\n    proxy[c[0]].apply(proxy, c[1])\n  })\n  self._buffer.length = 0\n  if (self._needsDrain) self.emit('drain')\n}\n\nProxyWriter.prototype.add = function (entry) {\n  // console.error(\"~~ proxy add\")\n  collect(entry)\n\n  if (!this._proxy) {\n    this._buffer.push(['add', [entry]])\n    this._needDrain = true\n    return false\n  }\n  return this._proxy.add(entry)\n}\n\nProxyWriter.prototype.write = function (c) {\n  // console.error('~~ proxy write')\n  if (!this._proxy) {\n    this._buffer.push(['write', [c]])\n    this._needDrain = true\n    return false\n  }\n  return this._proxy.write(c)\n}\n\nProxyWriter.prototype.end = function (c) {\n  // console.error('~~ proxy end')\n  if (!this._proxy) {\n    this._buffer.push(['end', [c]])\n    return false\n  }\n  return this._proxy.end(c)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/proxy-writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/reader.js":
/*!********************************************!*\
  !*** ./node_modules/fstream/lib/reader.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = Reader\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream)\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(ssr)/./node_modules/fstream/lib/get-type.js\")\nvar hardLinks = Reader.hardLinks = {}\nvar Abstract = __webpack_require__(/*! ./abstract.js */ \"(ssr)/./node_modules/fstream/lib/abstract.js\")\n\n// Must do this *before* loading the child classes\ninherits(Reader, Abstract)\n\nvar LinkReader = __webpack_require__(/*! ./link-reader.js */ \"(ssr)/./node_modules/fstream/lib/link-reader.js\")\n\nfunction Reader (props, currentStat) {\n  var self = this\n  if (!(self instanceof Reader)) return new Reader(props, currentStat)\n\n  if (typeof props === 'string') {\n    props = { path: props }\n  }\n\n  // polymorphism.\n  // call fstream.Reader(dir) to get a DirReader object, etc.\n  // Note that, unlike in the Writer case, ProxyReader is going\n  // to be the *normal* state of affairs, since we rarely know\n  // the type of a file prior to reading it.\n\n  var type\n  var ClassType\n\n  if (props.type && typeof props.type === 'function') {\n    type = props.type\n    ClassType = type\n  } else {\n    type = getType(props)\n    ClassType = Reader\n  }\n\n  if (currentStat && !type) {\n    type = getType(currentStat)\n    props[type] = true\n    props.type = type\n  }\n\n  switch (type) {\n    case 'Directory':\n      ClassType = __webpack_require__(/*! ./dir-reader.js */ \"(ssr)/./node_modules/fstream/lib/dir-reader.js\")\n      break\n\n    case 'Link':\n    // XXX hard links are just files.\n    // However, it would be good to keep track of files' dev+inode\n    // and nlink values, and create a HardLinkReader that emits\n    // a linkpath value of the original copy, so that the tar\n    // writer can preserve them.\n    // ClassType = HardLinkReader\n    // break\n\n    case 'File':\n      ClassType = __webpack_require__(/*! ./file-reader.js */ \"(ssr)/./node_modules/fstream/lib/file-reader.js\")\n      break\n\n    case 'SymbolicLink':\n      ClassType = LinkReader\n      break\n\n    case 'Socket':\n      ClassType = __webpack_require__(/*! ./socket-reader.js */ \"(ssr)/./node_modules/fstream/lib/socket-reader.js\")\n      break\n\n    case null:\n      ClassType = __webpack_require__(/*! ./proxy-reader.js */ \"(ssr)/./node_modules/fstream/lib/proxy-reader.js\")\n      break\n  }\n\n  if (!(self instanceof ClassType)) {\n    return new ClassType(props)\n  }\n\n  Abstract.call(self)\n\n  if (!props.path) {\n    self.error('Must provide a path', null, true)\n  }\n\n  self.readable = true\n  self.writable = false\n\n  self.type = type\n  self.props = props\n  self.depth = props.depth = props.depth || 0\n  self.parent = props.parent || null\n  self.root = props.root || (props.parent && props.parent.root) || self\n\n  self._path = self.path = path.resolve(props.path)\n  if (process.platform === 'win32') {\n    self.path = self._path = self.path.replace(/\\?/g, '_')\n    if (self._path.length >= 260) {\n      // how DOES one create files on the moon?\n      // if the path has spaces in it, then UNC will fail.\n      self._swallowErrors = true\n      // if (self._path.indexOf(\" \") === -1) {\n      self._path = '\\\\\\\\?\\\\' + self.path.replace(/\\//g, '\\\\')\n    // }\n    }\n  }\n  self.basename = props.basename = path.basename(self.path)\n  self.dirname = props.dirname = path.dirname(self.path)\n\n  // these have served their purpose, and are now just noisy clutter\n  props.parent = props.root = null\n\n  // console.error(\"\\n\\n\\n%s setting size to\", props.path, props.size)\n  self.size = props.size\n  self.filter = typeof props.filter === 'function' ? props.filter : null\n  if (props.sort === 'alpha') props.sort = alphasort\n\n  // start the ball rolling.\n  // this will stat the thing, and then call self._read()\n  // to start reading whatever it is.\n  // console.error(\"calling stat\", props.path, currentStat)\n  self._stat(currentStat)\n}\n\nfunction alphasort (a, b) {\n  return a === b ? 0\n    : a.toLowerCase() > b.toLowerCase() ? 1\n      : a.toLowerCase() < b.toLowerCase() ? -1\n        : a > b ? 1\n          : -1\n}\n\nReader.prototype._stat = function (currentStat) {\n  var self = this\n  var props = self.props\n  var stat = props.follow ? 'stat' : 'lstat'\n  // console.error(\"Reader._stat\", self._path, currentStat)\n  if (currentStat) process.nextTick(statCb.bind(null, null, currentStat))\n  else fs[stat](self._path, statCb)\n\n  function statCb (er, props_) {\n    // console.error(\"Reader._stat, statCb\", self._path, props_, props_.nlink)\n    if (er) return self.error(er)\n\n    Object.keys(props_).forEach(function (k) {\n      props[k] = props_[k]\n    })\n\n    // if it's not the expected size, then abort here.\n    if (undefined !== self.size && props.size !== self.size) {\n      return self.error('incorrect size')\n    }\n    self.size = props.size\n\n    var type = getType(props)\n    var handleHardlinks = props.hardlinks !== false\n\n    // special little thing for handling hardlinks.\n    if (handleHardlinks && type !== 'Directory' && props.nlink && props.nlink > 1) {\n      var k = props.dev + ':' + props.ino\n      // console.error(\"Reader has nlink\", self._path, k)\n      if (hardLinks[k] === self._path || !hardLinks[k]) {\n        hardLinks[k] = self._path\n      } else {\n        // switch into hardlink mode.\n        type = self.type = self.props.type = 'Link'\n        self.Link = self.props.Link = true\n        self.linkpath = self.props.linkpath = hardLinks[k]\n        // console.error(\"Hardlink detected, switching mode\", self._path, self.linkpath)\n        // Setting __proto__ would arguably be the \"correct\"\n        // approach here, but that just seems too wrong.\n        self._stat = self._read = LinkReader.prototype._read\n      }\n    }\n\n    if (self.type && self.type !== type) {\n      self.error('Unexpected type: ' + type)\n    }\n\n    // if the filter doesn't pass, then just skip over this one.\n    // still have to emit end so that dir-walking can move on.\n    if (self.filter) {\n      var who = self._proxy || self\n      // special handling for ProxyReaders\n      if (!self.filter.call(who, who, props)) {\n        if (!self._disowned) {\n          self.abort()\n          self.emit('end')\n          self.emit('close')\n        }\n        return\n      }\n    }\n\n    // last chance to abort or disown before the flow starts!\n    var events = ['_stat', 'stat', 'ready']\n    var e = 0\n    ;(function go () {\n      if (self._aborted) {\n        self.emit('end')\n        self.emit('close')\n        return\n      }\n\n      if (self._paused && self.type !== 'Directory') {\n        self.once('resume', go)\n        return\n      }\n\n      var ev = events[e++]\n      if (!ev) {\n        return self._read()\n      }\n      self.emit(ev, props)\n      go()\n    })()\n  }\n}\n\nReader.prototype.pipe = function (dest) {\n  var self = this\n  if (typeof dest.add === 'function') {\n    // piping to a multi-compatible, and we've got directory entries.\n    self.on('entry', function (entry) {\n      var ret = dest.add(entry)\n      if (ret === false) {\n        self.pause()\n      }\n    })\n  }\n\n  // console.error(\"R Pipe apply Stream Pipe\")\n  return Stream.prototype.pipe.apply(this, arguments)\n}\n\nReader.prototype.pause = function (who) {\n  this._paused = true\n  who = who || this\n  this.emit('pause', who)\n  if (this._stream) this._stream.pause(who)\n}\n\nReader.prototype.resume = function (who) {\n  this._paused = false\n  who = who || this\n  this.emit('resume', who)\n  if (this._stream) this._stream.resume(who)\n  this._read()\n}\n\nReader.prototype._read = function () {\n  this.error('Cannot read unknown type: ' + this.type)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/socket-reader.js":
/*!***************************************************!*\
  !*** ./node_modules/fstream/lib/socket-reader.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Just get the stats, and then don't do anything.\n// You can't really \"read\" from a socket.  You \"connect\" to it.\n// Mostly, this is here so that reading a dir with a socket in it\n// doesn't blow up.\n\nmodule.exports = SocketReader\n\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\n\ninherits(SocketReader, Reader)\n\nfunction SocketReader (props) {\n  var self = this\n  if (!(self instanceof SocketReader)) {\n    throw new Error('SocketReader must be called as constructor.')\n  }\n\n  if (!(props.type === 'Socket' && props.Socket)) {\n    throw new Error('Non-socket type ' + props.type)\n  }\n\n  Reader.call(self, props)\n}\n\nSocketReader.prototype._read = function () {\n  var self = this\n  if (self._paused) return\n  // basically just a no-op, since we got all the info we have\n  // from the _stat method\n  if (!self._ended) {\n    self.emit('end')\n    self.emit('close')\n    self._ended = true\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvc29ja2V0LXJlYWRlci5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSxlQUFlLG1CQUFPLENBQUMsMkRBQVU7QUFDakMsYUFBYSxtQkFBTyxDQUFDLCtEQUFhOztBQUVsQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jbGllbnQvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvc29ja2V0LXJlYWRlci5qcz83YzUxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEp1c3QgZ2V0IHRoZSBzdGF0cywgYW5kIHRoZW4gZG9uJ3QgZG8gYW55dGhpbmcuXG4vLyBZb3UgY2FuJ3QgcmVhbGx5IFwicmVhZFwiIGZyb20gYSBzb2NrZXQuICBZb3UgXCJjb25uZWN0XCIgdG8gaXQuXG4vLyBNb3N0bHksIHRoaXMgaXMgaGVyZSBzbyB0aGF0IHJlYWRpbmcgYSBkaXIgd2l0aCBhIHNvY2tldCBpbiBpdFxuLy8gZG9lc24ndCBibG93IHVwLlxuXG5tb2R1bGUuZXhwb3J0cyA9IFNvY2tldFJlYWRlclxuXG52YXIgaW5oZXJpdHMgPSByZXF1aXJlKCdpbmhlcml0cycpXG52YXIgUmVhZGVyID0gcmVxdWlyZSgnLi9yZWFkZXIuanMnKVxuXG5pbmhlcml0cyhTb2NrZXRSZWFkZXIsIFJlYWRlcilcblxuZnVuY3Rpb24gU29ja2V0UmVhZGVyIChwcm9wcykge1xuICB2YXIgc2VsZiA9IHRoaXNcbiAgaWYgKCEoc2VsZiBpbnN0YW5jZW9mIFNvY2tldFJlYWRlcikpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ1NvY2tldFJlYWRlciBtdXN0IGJlIGNhbGxlZCBhcyBjb25zdHJ1Y3Rvci4nKVxuICB9XG5cbiAgaWYgKCEocHJvcHMudHlwZSA9PT0gJ1NvY2tldCcgJiYgcHJvcHMuU29ja2V0KSkge1xuICAgIHRocm93IG5ldyBFcnJvcignTm9uLXNvY2tldCB0eXBlICcgKyBwcm9wcy50eXBlKVxuICB9XG5cbiAgUmVhZGVyLmNhbGwoc2VsZiwgcHJvcHMpXG59XG5cblNvY2tldFJlYWRlci5wcm90b3R5cGUuX3JlYWQgPSBmdW5jdGlvbiAoKSB7XG4gIHZhciBzZWxmID0gdGhpc1xuICBpZiAoc2VsZi5fcGF1c2VkKSByZXR1cm5cbiAgLy8gYmFzaWNhbGx5IGp1c3QgYSBuby1vcCwgc2luY2Ugd2UgZ290IGFsbCB0aGUgaW5mbyB3ZSBoYXZlXG4gIC8vIGZyb20gdGhlIF9zdGF0IG1ldGhvZFxuICBpZiAoIXNlbGYuX2VuZGVkKSB7XG4gICAgc2VsZi5lbWl0KCdlbmQnKVxuICAgIHNlbGYuZW1pdCgnY2xvc2UnKVxuICAgIHNlbGYuX2VuZGVkID0gdHJ1ZVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/socket-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/writer.js":
/*!********************************************!*\
  !*** ./node_modules/fstream/lib/writer.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = Writer\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar rimraf = __webpack_require__(/*! rimraf */ \"rimraf\")\nvar mkdir = __webpack_require__(/*! mkdirp */ \"(ssr)/./node_modules/mkdirp/index.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar umask = process.platform === 'win32' ? 0 : process.umask()\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(ssr)/./node_modules/fstream/lib/get-type.js\")\nvar Abstract = __webpack_require__(/*! ./abstract.js */ \"(ssr)/./node_modules/fstream/lib/abstract.js\")\n\n// Must do this *before* loading the child classes\ninherits(Writer, Abstract)\n\nWriter.dirmode = parseInt('0777', 8) & (~umask)\nWriter.filemode = parseInt('0666', 8) & (~umask)\n\nvar DirWriter = __webpack_require__(/*! ./dir-writer.js */ \"(ssr)/./node_modules/fstream/lib/dir-writer.js\")\nvar LinkWriter = __webpack_require__(/*! ./link-writer.js */ \"(ssr)/./node_modules/fstream/lib/link-writer.js\")\nvar FileWriter = __webpack_require__(/*! ./file-writer.js */ \"(ssr)/./node_modules/fstream/lib/file-writer.js\")\nvar ProxyWriter = __webpack_require__(/*! ./proxy-writer.js */ \"(ssr)/./node_modules/fstream/lib/proxy-writer.js\")\n\n// props is the desired state.  current is optionally the current stat,\n// provided here so that subclasses can avoid statting the target\n// more than necessary.\nfunction Writer (props, current) {\n  var self = this\n\n  if (typeof props === 'string') {\n    props = { path: props }\n  }\n\n  // polymorphism.\n  // call fstream.Writer(dir) to get a DirWriter object, etc.\n  var type = getType(props)\n  var ClassType = Writer\n\n  switch (type) {\n    case 'Directory':\n      ClassType = DirWriter\n      break\n    case 'File':\n      ClassType = FileWriter\n      break\n    case 'Link':\n    case 'SymbolicLink':\n      ClassType = LinkWriter\n      break\n    case null:\n    default:\n      // Don't know yet what type to create, so we wrap in a proxy.\n      ClassType = ProxyWriter\n      break\n  }\n\n  if (!(self instanceof ClassType)) return new ClassType(props)\n\n  // now get down to business.\n\n  Abstract.call(self)\n\n  if (!props.path) self.error('Must provide a path', null, true)\n\n  // props is what we want to set.\n  // set some convenience properties as well.\n  self.type = props.type\n  self.props = props\n  self.depth = props.depth || 0\n  self.clobber = props.clobber === false ? props.clobber : true\n  self.parent = props.parent || null\n  self.root = props.root || (props.parent && props.parent.root) || self\n\n  self._path = self.path = path.resolve(props.path)\n  if (process.platform === 'win32') {\n    self.path = self._path = self.path.replace(/\\?/g, '_')\n    if (self._path.length >= 260) {\n      self._swallowErrors = true\n      self._path = '\\\\\\\\?\\\\' + self.path.replace(/\\//g, '\\\\')\n    }\n  }\n  self.basename = path.basename(props.path)\n  self.dirname = path.dirname(props.path)\n  self.linkpath = props.linkpath || null\n\n  props.parent = props.root = null\n\n  // console.error(\"\\n\\n\\n%s setting size to\", props.path, props.size)\n  self.size = props.size\n\n  if (typeof props.mode === 'string') {\n    props.mode = parseInt(props.mode, 8)\n  }\n\n  self.readable = false\n  self.writable = true\n\n  // buffer until ready, or while handling another entry\n  self._buffer = []\n  self.ready = false\n\n  self.filter = typeof props.filter === 'function' ? props.filter : null\n\n  // start the ball rolling.\n  // this checks what's there already, and then calls\n  // self._create() to call the impl-specific creation stuff.\n  self._stat(current)\n}\n\n// Calling this means that it's something we can't create.\n// Just assert that it's already there, otherwise raise a warning.\nWriter.prototype._create = function () {\n  var self = this\n  fs[self.props.follow ? 'stat' : 'lstat'](self._path, function (er) {\n    if (er) {\n      return self.warn('Cannot create ' + self._path + '\\n' +\n        'Unsupported type: ' + self.type, 'ENOTSUP')\n    }\n    self._finish()\n  })\n}\n\nWriter.prototype._stat = function (current) {\n  var self = this\n  var props = self.props\n  var stat = props.follow ? 'stat' : 'lstat'\n  var who = self._proxy || self\n\n  if (current) statCb(null, current)\n  else fs[stat](self._path, statCb)\n\n  function statCb (er, current) {\n    if (self.filter && !self.filter.call(who, who, current)) {\n      self._aborted = true\n      self.emit('end')\n      self.emit('close')\n      return\n    }\n\n    // if it's not there, great.  We'll just create it.\n    // if it is there, then we'll need to change whatever differs\n    if (er || !current) {\n      return create(self)\n    }\n\n    self._old = current\n    var currentType = getType(current)\n\n    // if it's a type change, then we need to clobber or error.\n    // if it's not a type change, then let the impl take care of it.\n    if (currentType !== self.type || self.type === 'File' && current.nlink > 1) {\n      return rimraf(self._path, function (er) {\n        if (er) return self.error(er)\n        self._old = null\n        create(self)\n      })\n    }\n\n    // otherwise, just handle in the app-specific way\n    // this creates a fs.WriteStream, or mkdir's, or whatever\n    create(self)\n  }\n}\n\nfunction create (self) {\n  // console.error(\"W create\", self._path, Writer.dirmode)\n\n  // XXX Need to clobber non-dirs that are in the way,\n  // unless { clobber: false } in the props.\n  mkdir(path.dirname(self._path), Writer.dirmode, function (er, made) {\n    // console.error(\"W created\", path.dirname(self._path), er)\n    if (er) return self.error(er)\n\n    // later on, we have to set the mode and owner for these\n    self._madeDir = made\n    return self._create()\n  })\n}\n\nfunction endChmod (self, want, current, path, cb) {\n  var wantMode = want.mode\n  var chmod = want.follow || self.type !== 'SymbolicLink'\n    ? 'chmod' : 'lchmod'\n\n  if (!fs[chmod]) return cb()\n  if (typeof wantMode !== 'number') return cb()\n\n  var curMode = current.mode & parseInt('0777', 8)\n  wantMode = wantMode & parseInt('0777', 8)\n  if (wantMode === curMode) return cb()\n\n  fs[chmod](path, wantMode, cb)\n}\n\nfunction endChown (self, want, current, path, cb) {\n  // Don't even try it unless root.  Too easy to EPERM.\n  if (process.platform === 'win32') return cb()\n  if (!process.getuid || process.getuid() !== 0) return cb()\n  if (typeof want.uid !== 'number' &&\n    typeof want.gid !== 'number') return cb()\n\n  if (current.uid === want.uid &&\n    current.gid === want.gid) return cb()\n\n  var chown = (self.props.follow || self.type !== 'SymbolicLink')\n    ? 'chown' : 'lchown'\n  if (!fs[chown]) return cb()\n\n  if (typeof want.uid !== 'number') want.uid = current.uid\n  if (typeof want.gid !== 'number') want.gid = current.gid\n\n  fs[chown](path, want.uid, want.gid, cb)\n}\n\nfunction endUtimes (self, want, current, path, cb) {\n  if (!fs.utimes || process.platform === 'win32') return cb()\n\n  var utimes = (want.follow || self.type !== 'SymbolicLink')\n    ? 'utimes' : 'lutimes'\n\n  if (utimes === 'lutimes' && !fs[utimes]) {\n    utimes = 'utimes'\n  }\n\n  if (!fs[utimes]) return cb()\n\n  var curA = current.atime\n  var curM = current.mtime\n  var meA = want.atime\n  var meM = want.mtime\n\n  if (meA === undefined) meA = curA\n  if (meM === undefined) meM = curM\n\n  if (!isDate(meA)) meA = new Date(meA)\n  if (!isDate(meM)) meA = new Date(meM)\n\n  if (meA.getTime() === curA.getTime() &&\n    meM.getTime() === curM.getTime()) return cb()\n\n  fs[utimes](path, meA, meM, cb)\n}\n\n// XXX This function is beastly.  Break it up!\nWriter.prototype._finish = function () {\n  var self = this\n\n  if (self._finishing) return\n  self._finishing = true\n\n  // console.error(\" W Finish\", self._path, self.size)\n\n  // set up all the things.\n  // At this point, we're already done writing whatever we've gotta write,\n  // adding files to the dir, etc.\n  var todo = 0\n  var errState = null\n  var done = false\n\n  if (self._old) {\n    // the times will almost *certainly* have changed.\n    // adds the utimes syscall, but remove another stat.\n    self._old.atime = new Date(0)\n    self._old.mtime = new Date(0)\n    // console.error(\" W Finish Stale Stat\", self._path, self.size)\n    setProps(self._old)\n  } else {\n    var stat = self.props.follow ? 'stat' : 'lstat'\n    // console.error(\" W Finish Stating\", self._path, self.size)\n    fs[stat](self._path, function (er, current) {\n      // console.error(\" W Finish Stated\", self._path, self.size, current)\n      if (er) {\n        // if we're in the process of writing out a\n        // directory, it's very possible that the thing we're linking to\n        // doesn't exist yet (especially if it was intended as a symlink),\n        // so swallow ENOENT errors here and just soldier on.\n        if (er.code === 'ENOENT' &&\n          (self.type === 'Link' || self.type === 'SymbolicLink') &&\n          process.platform === 'win32') {\n          self.ready = true\n          self.emit('ready')\n          self.emit('end')\n          self.emit('close')\n          self.end = self._finish = function () {}\n          return\n        } else return self.error(er)\n      }\n      setProps(self._old = current)\n    })\n  }\n\n  return\n\n  function setProps (current) {\n    todo += 3\n    endChmod(self, self.props, current, self._path, next('chmod'))\n    endChown(self, self.props, current, self._path, next('chown'))\n    endUtimes(self, self.props, current, self._path, next('utimes'))\n  }\n\n  function next (what) {\n    return function (er) {\n      // console.error(\"   W Finish\", what, todo)\n      if (errState) return\n      if (er) {\n        er.fstream_finish_call = what\n        return self.error(errState = er)\n      }\n      if (--todo > 0) return\n      if (done) return\n      done = true\n\n      // we may still need to set the mode/etc. on some parent dirs\n      // that were created previously.  delay end/close until then.\n      if (!self._madeDir) return end()\n      else endMadeDir(self, self._path, end)\n\n      function end (er) {\n        if (er) {\n          er.fstream_finish_call = 'setupMadeDir'\n          return self.error(er)\n        }\n        // all the props have been set, so we're completely done.\n        self.emit('end')\n        self.emit('close')\n      }\n    }\n  }\n}\n\nfunction endMadeDir (self, p, cb) {\n  var made = self._madeDir\n  // everything *between* made and path.dirname(self._path)\n  // needs to be set up.  Note that this may just be one dir.\n  var d = path.dirname(p)\n\n  endMadeDir_(self, d, function (er) {\n    if (er) return cb(er)\n    if (d === made) {\n      return cb()\n    }\n    endMadeDir(self, d, cb)\n  })\n}\n\nfunction endMadeDir_ (self, p, cb) {\n  var dirProps = {}\n  Object.keys(self.props).forEach(function (k) {\n    dirProps[k] = self.props[k]\n\n    // only make non-readable dirs if explicitly requested.\n    if (k === 'mode' && self.type !== 'Directory') {\n      dirProps[k] = dirProps[k] | parseInt('0111', 8)\n    }\n  })\n\n  var todo = 3\n  var errState = null\n  fs.stat(p, function (er, current) {\n    if (er) return cb(errState = er)\n    endChmod(self, dirProps, current, p, next)\n    endChown(self, dirProps, current, p, next)\n    endUtimes(self, dirProps, current, p, next)\n  })\n\n  function next (er) {\n    if (errState) return\n    if (er) return cb(errState = er)\n    if (--todo === 0) return cb()\n  }\n}\n\nWriter.prototype.pipe = function () {\n  this.error(\"Can't pipe from writable stream\")\n}\n\nWriter.prototype.add = function () {\n  this.error(\"Can't add to non-Directory type\")\n}\n\nWriter.prototype.write = function () {\n  return true\n}\n\nfunction objectToString (d) {\n  return Object.prototype.toString.call(d)\n}\n\nfunction isDate (d) {\n  return typeof d === 'object' && objectToString(d) === '[object Date]'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/writer.js\n");

/***/ })

};
;