"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pms/manage_tickets/page",{

/***/ "(app-pages-browser)/./app/pms/manage_tickets/components/ticket-sidebar.tsx":
/*!**************************************************************!*\
  !*** ./app/pms/manage_tickets/components/ticket-sidebar.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TicketSidebar: function() { return /* binding */ TicketSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./components/ui/sheet.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _comment_section__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./comment-section */ \"(app-pages-browser)/./app/pms/manage_tickets/components/comment-section.tsx\");\n/* harmony import */ var _tag_manager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./tag-manager */ \"(app-pages-browser)/./app/pms/manage_tickets/components/tag-manager.tsx\");\n/* harmony import */ var _TicketContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../TicketContext */ \"(app-pages-browser)/./app/pms/manage_tickets/TicketContext.tsx\");\n/* harmony import */ var _activity_section__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./activity-section */ \"(app-pages-browser)/./app/pms/manage_tickets/components/activity-section.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ TicketSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TicketSidebar(param) {\n    let { ticket: initialTicket, isOpen, onClose, onOpenInNewTab, onTagsUpdated } = param;\n    var _ticket_pipeline, _ticket_pipeline1;\n    _s();\n    // All hooks must be called at the top, before any early returns\n    const [commentsCount, setCommentsCount] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(0);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(\"details\");\n    const { users, currentUser, setTickets, tickets } = (0,react__WEBPACK_IMPORTED_MODULE_7__.useContext)(_TicketContext__WEBPACK_IMPORTED_MODULE_10__.TicketContext);\n    const hasLoadedCommentsCount = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n    // Inline edit state for priority\n    const [editingPriority, setEditingPriority] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    var _initialTicket_priority;\n    const [priorityValue, setPriorityValue] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)((_initialTicket_priority = initialTicket === null || initialTicket === void 0 ? void 0 : initialTicket.priority) !== null && _initialTicket_priority !== void 0 ? _initialTicket_priority : \"low\");\n    const [priorityLoading, setPriorityLoading] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [priorityError, setPriorityError] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(\"\");\n    // Now, after all hooks, handle ticket logic\n    let ticket = tickets.find((t)=>t.id === (initialTicket === null || initialTicket === void 0 ? void 0 : initialTicket.id)) || initialTicket;\n    if (ticket && ticket.currentStage && ((_ticket_pipeline = ticket.pipeline) === null || _ticket_pipeline === void 0 ? void 0 : _ticket_pipeline.stages)) {\n        const pipelineStage = ticket.pipeline.stages.find((ps)=>ps.id === ticket.currentStage.pipelineStageId);\n        if (pipelineStage && !ticket.currentStage.name) {\n            ticket = {\n                ...ticket,\n                currentStage: {\n                    ...ticket.currentStage,\n                    name: pipelineStage.name\n                }\n            };\n        }\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        async function fetchCommentsCount() {\n            if (!ticket) return;\n            try {\n                var _data_data;\n                const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_6__.comment_routes.GET_COMMENTS_BY_TICKET(ticket.id));\n                const data = await res.json();\n                setCommentsCount(((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.length) || 0);\n            } catch (e) {\n                setCommentsCount(0);\n            }\n        }\n        if (isOpen && ticket && hasLoadedCommentsCount.current !== ticket.id) {\n            hasLoadedCommentsCount.current = ticket.id;\n            fetchCommentsCount();\n        }\n        if (!isOpen) {\n            hasLoadedCommentsCount.current = null;\n        }\n    }, [\n        isOpen,\n        ticket\n    ]);\n    if (!ticket) return null;\n    const priorityColors = {\n        low: \"bg-gray-100 text-gray-800\",\n        medium: \"bg-blue-100 text-blue-800\",\n        high: \"bg-orange-100 text-orange-800\",\n        urgent: \"bg-red-100 text-red-800\"\n    };\n    const currentStage = ticket.currentStage;\n    const assignedUser = currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedUser;\n    let assignedToDisplay;\n    if (currentUser && ((currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedTo) === currentUser.id || (currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedTo) === currentUser.username)) {\n        assignedToDisplay = \"You\";\n    } else if (assignedUser) {\n        assignedToDisplay = assignedUser.username || assignedUser.id;\n    } else {\n        assignedToDisplay = (currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedTo) || \"Unassigned\";\n    }\n    const badgeColors = [\n        \"bg-gray-200 text-gray-800\",\n        \"bg-blue-200 text-blue-800\",\n        \"bg-green-200 text-green-800\",\n        \"bg-yellow-200 text-yellow-800\",\n        \"bg-purple-200 text-purple-800\",\n        \"bg-pink-200 text-pink-800\",\n        \"bg-orange-200 text-orange-800\",\n        \"bg-red-200 text-red-800\"\n    ];\n    let stageColor = \"bg-gray-200 text-gray-800\";\n    if ((_ticket_pipeline1 = ticket.pipeline) === null || _ticket_pipeline1 === void 0 ? void 0 : _ticket_pipeline1.stages) {\n        const idx = ticket.pipeline.stages.findIndex((s)=>{\n            var _ticket_currentStage;\n            return s.id === ((_ticket_currentStage = ticket.currentStage) === null || _ticket_currentStage === void 0 ? void 0 : _ticket_currentStage.pipelineStageId);\n        });\n        if (idx !== -1) {\n            stageColor = badgeColors[idx % badgeColors.length];\n        }\n    }\n    const handleCommentAdded = ()=>{\n        setCommentsCount((prev)=>prev + 1);\n    };\n    const handleTagsUpdated = ()=>{\n        onTagsUpdated === null || onTagsUpdated === void 0 ? void 0 : onTagsUpdated();\n    };\n    // Update ticket API call\n    async function updateTicketField(field, value) {\n        setPriorityLoading(true);\n        setPriorityError(\"\");\n        try {\n            const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_6__.ticket_routes.UPDATE_TICKET(ticket.id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    ticketId: ticket.id,\n                    updatedBy: String(currentUser === null || currentUser === void 0 ? void 0 : currentUser.id),\n                    [field]: value\n                })\n            });\n            if (!res.ok) throw new Error(\"Failed to update ticket\");\n            setPriorityValue(value);\n            setTickets((prev)=>prev.map((t)=>t.id === ticket.id ? {\n                        ...t,\n                        priority: value\n                    } : t));\n            setEditingPriority(false);\n        } catch (e) {\n            setPriorityError(e.message || \"Error updating priority\");\n        } finally{\n            setPriorityLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.Sheet, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetContent, {\n            className: \"w-full sm:max-w-lg overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 pr-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetTitle, {\n                                        className: \"text-lg mb-2 leading-tight\",\n                                        children: ticket.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap items-center gap-2\",\n                                        children: [\n                                            editingPriority ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                                        value: priorityValue,\n                                                        onValueChange: (val)=>updateTicketField(\"priority\", val),\n                                                        disabled: priorityLoading,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                                className: priorityColors[priorityValue] + \" min-w-[100px]\",\n                                                                children: [\n                                                                    priorityLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"animate-spin h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 165,\n                                                                        columnNumber: 44\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"mr-1 h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 165,\n                                                                        columnNumber: 91\n                                                                    }, this),\n                                                                    priorityValue\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                        value: \"low\",\n                                                                        children: \"Low\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 169,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                        value: \"medium\",\n                                                                        children: \"Medium\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 170,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                        value: \"high\",\n                                                                        children: \"High\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                        value: \"urgent\",\n                                                                        children: \"Urgent\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                        lineNumber: 172,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    priorityError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-red-600 ml-2\",\n                                                        children: priorityError\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 39\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                className: priorityColors[priorityValue],\n                                                onClick: ()=>setEditingPriority(true),\n                                                style: {\n                                                    cursor: \"pointer\"\n                                                },\n                                                title: \"Click to edit\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"mr-1 h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    priorityValue\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentStage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                className: stageColor,\n                                                variant: \"secondary\",\n                                                children: currentStage.name || currentStage.pipelineStageId\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, this),\n                                            ticket.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                    className: \"\".concat(tag.color, \" text-xs\"),\n                                                    children: tag.tagName || tag.name\n                                                }, tag.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>onOpenInNewTab(ticket.id),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                        value: activeTab,\n                        onValueChange: setActiveTab,\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                className: \"grid w-full grid-cols-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"details\",\n                                        className: \"text-xs\",\n                                        children: \"Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"comments\",\n                                        className: \"text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Comments\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-1 text-blue-600 font-bold\",\n                                                children: [\n                                                    \"(\",\n                                                    commentsCount,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"tags\",\n                                        className: \"text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Tags\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"activity\",\n                                        className: \"text-xs\",\n                                        children: \"Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"details\",\n                                className: \"space-y-6 mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-3\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 text-sm leading-relaxed\",\n                                                children: ticket.description || \"No description provided\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                        children: \"Assigned To\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: assignedUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                                    className: \"h-5 w-5\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.AvatarImage, {\n                                                                            src: assignedUser.avatar || \" \",\n                                                                            alt: assignedToDisplay\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                            lineNumber: 236,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                                            className: \"text-xs\",\n                                                                            children: assignedUser.username ? assignedUser.username[0].toUpperCase() : \"\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                            lineNumber: 237,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: assignedToDisplay\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                    lineNumber: 241,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: assignedToDisplay\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentStage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                        children: \"Due Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm \".concat(currentStage.dueAt && new Date(currentStage.dueAt) < new Date() ? \"text-red-600\" : \"\"),\n                                                                children: currentStage.dueAt ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(currentStage.dueAt), \"PPP\") : \"No due date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                        children: \"Created\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(ticket.createdAt), \"PPP\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                        children: \"Last Updated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(ticket.updatedAt), \"PPP\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"comments\",\n                                className: \"space-y-4 mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_comment_section__WEBPACK_IMPORTED_MODULE_8__.CommentSection, {\n                                    ticketId: ticket.id,\n                                    createdBy: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.username) || \"\",\n                                    setCommentsCount: setCommentsCount,\n                                    onCommentsChange: (newComments)=>{\n                                        setTickets((prev)=>prev.map((t)=>t.id === ticket.id ? {\n                                                    ...t,\n                                                    comments: newComments\n                                                } : t));\n                                    },\n                                    isActive: activeTab === \"comments\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"tags\",\n                                className: \"space-y-4 mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tag_manager__WEBPACK_IMPORTED_MODULE_9__.TagManager, {\n                                    ticketId: ticket.id,\n                                    assignedTags: ticket.tags,\n                                    onTagsUpdated: handleTagsUpdated,\n                                    onTagsChange: (newTags)=>{\n                                        setTickets((prev)=>prev.map((t)=>t.id === ticket.id ? {\n                                                    ...t,\n                                                    tags: newTags\n                                                } : t));\n                                    },\n                                    createdBy: ticket.createdBy || \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"activity\",\n                                className: \"space-y-4 mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_activity_section__WEBPACK_IMPORTED_MODULE_11__.ActivitySection, {\n                                    ticketId: ticket.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n_s(TicketSidebar, \"kJn1Xh+J0Vgr+I6v1QlTJn7sRuU=\");\n_c = TicketSidebar;\nvar _c;\n$RefreshReg$(_c, \"TicketSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pms/manage_tickets/components/ticket-sidebar.tsx\n"));

/***/ })

});