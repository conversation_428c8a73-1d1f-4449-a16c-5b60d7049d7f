"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pms/manage_tickets/[id]/page",{

/***/ "(app-pages-browser)/./app/pms/manage_tickets/TicketContext.tsx":
/*!**************************************************!*\
  !*** ./app/pms/manage_tickets/TicketContext.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TicketContext: function() { return /* binding */ TicketContext; },\n/* harmony export */   TicketProvider: function() { return /* binding */ TicketProvider; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ TicketContext,TicketProvider auto */ \nvar _s = $RefreshSig$();\n\nconst TicketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    tickets: [],\n    setTickets: ()=>{},\n    users: [],\n    setUsers: ()=>{},\n    currentUser: null,\n    setCurrentUser: ()=>{}\n});\nfunction TicketProvider(param) {\n    let { children, initialCurrentUser } = param;\n    _s();\n    const [tickets, setTickets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialCurrentUser || null);\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            tickets,\n            setTickets,\n            users,\n            setUsers,\n            currentUser,\n            setCurrentUser\n        }), [\n        tickets,\n        users,\n        currentUser\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TicketContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\TicketContext.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_s(TicketProvider, \"p2c7vWTc0uHsthWPQG8MGMrP3kg=\");\n_c = TicketProvider;\nvar _c;\n$RefreshReg$(_c, \"TicketProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pms/manage_tickets/TicketContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/pms/manage_tickets/[id]/page.tsx":
/*!**********************************************!*\
  !*** ./app/pms/manage_tickets/[id]/page.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TicketDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Flag_MessageSquare_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Flag,MessageSquare,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Flag_MessageSquare_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Flag,MessageSquare,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Flag_MessageSquare_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Flag,MessageSquare,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flag.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Flag_MessageSquare_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Flag,MessageSquare,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Flag_MessageSquare_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Flag,MessageSquare,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var _tickets__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../tickets */ \"(app-pages-browser)/./app/pms/manage_tickets/tickets.ts\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var _components_comment_section__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/comment-section */ \"(app-pages-browser)/./app/pms/manage_tickets/components/comment-section.tsx\");\n/* harmony import */ var _components_tag_manager__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/tag-manager */ \"(app-pages-browser)/./app/pms/manage_tickets/components/tag-manager.tsx\");\n/* harmony import */ var _TicketContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../TicketContext */ \"(app-pages-browser)/./app/pms/manage_tickets/TicketContext.tsx\");\n/* harmony import */ var _components_activity_section__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../components/activity-section */ \"(app-pages-browser)/./app/pms/manage_tickets/components/activity-section.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TicketDetailPage() {\n    var _ticket_comments, _ticket_pipeline;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [ticket, setTicket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"details\");\n    const { currentUser, tickets, setTickets } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_TicketContext__WEBPACK_IMPORTED_MODULE_11__.TicketContext);\n    var _ticket_comments_length;\n    const [commentsCount, setCommentsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_ticket_comments_length = ticket === null || ticket === void 0 ? void 0 : (_ticket_comments = ticket.comments) === null || _ticket_comments === void 0 ? void 0 : _ticket_comments.length) !== null && _ticket_comments_length !== void 0 ? _ticket_comments_length : 0);\n    const hasLoadedTicket = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const hasLoadedCommentsCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadTicket = async ()=>{\n            try {\n                var _data_pipeline;\n                const data = await (0,_tickets__WEBPACK_IMPORTED_MODULE_7__.fetchTicket)(params.id);\n                if (data && !data.currentStage && data.currentStageId && Array.isArray(data.stages)) {\n                    data.currentStage = data.stages.find((s)=>s.pipelineStageId === data.currentStageId);\n                }\n                if (data && data.currentStage && ((_data_pipeline = data.pipeline) === null || _data_pipeline === void 0 ? void 0 : _data_pipeline.stages)) {\n                    const pipelineStage = data.pipeline.stages.find((ps)=>ps.id === data.currentStage.pipelineStageId);\n                    if (pipelineStage) {\n                        data.currentStage.name = pipelineStage.name;\n                    }\n                }\n                setTicket(data);\n                setTickets((prev)=>{\n                    const idx = prev.findIndex((t)=>t.id === data.id);\n                    if (idx !== -1) {\n                        const updated = [\n                            ...prev\n                        ];\n                        updated[idx] = data;\n                        return updated;\n                    } else {\n                        return [\n                            ...prev,\n                            data\n                        ];\n                    }\n                });\n            } catch (error) {\n                /* eslint-disable */ console.error(...oo_tx(\"1626987005_65_8_65_55_11\", \"Failed to fetch ticket:\", error));\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        if (params.id && hasLoadedTicket.current !== params.id) {\n            hasLoadedTicket.current = params.id;\n            loadTicket();\n        }\n    }, [\n        params.id,\n        setTickets\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        async function fetchCommentsCount() {\n            if (!ticket) return;\n            try {\n                var _data_data;\n                const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.comment_routes.GET_COMMENTS_BY_TICKET(ticket.id));\n                const data = await res.json();\n                setCommentsCount(((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.length) || 0);\n            } catch (e) {\n                setCommentsCount(0);\n            }\n        }\n        if (ticket && hasLoadedCommentsCount.current !== ticket.id) {\n            hasLoadedCommentsCount.current = ticket.id;\n            fetchCommentsCount();\n        }\n    }, [\n        ticket\n    ]);\n    const handleTagsUpdated = async ()=>{\n        if (params.id) {\n            const data = await (0,_tickets__WEBPACK_IMPORTED_MODULE_7__.fetchTicket)(params.id);\n            setTicket(data);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Flag_MessageSquare_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-8 w-8 animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this);\n    }\n    if (!ticket) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                    children: \"Ticket not found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-4\",\n                    children: \"The ticket you're looking for doesn't exist.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    onClick: ()=>router.push(\"/pms/manage_tickets\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Flag_MessageSquare_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"mr-2 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        \"Back to Tickets\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n            lineNumber: 112,\n            columnNumber: 7\n        }, this);\n    }\n    const currentStage = ticket.currentStage;\n    const assignedUser = currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedUser;\n    let assignedToDisplay;\n    if (currentUser && ((currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedTo) === currentUser.id || (currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedTo) === currentUser.username)) {\n        assignedToDisplay = \"You\";\n    } else if (assignedUser) {\n        assignedToDisplay = assignedUser.username || assignedUser.id;\n    } else {\n        assignedToDisplay = (currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedTo) || \"Unassigned\";\n    }\n    const assigneeDisplay = ticket.createdBy || \"Unassigned\";\n    const ownerDisplay = ticket.owner || \"\";\n    const priorityColors = {\n        Low: \"bg-gray-100 text-gray-800 hover:bg-gray-50\",\n        Medium: \"bg-blue-100 text-blue-800 hover:bg-blue-50\",\n        High: \"bg-orange-100 text-orange-800 hover:bg-orange-50\",\n        Urgent: \"bg-red-100 text-red-800 hover:bg-red-50\",\n        // Also support lowercase versions for compatibility\n        low: \"bg-gray-100 text-gray-800 hover:bg-gray-50\",\n        medium: \"bg-blue-100 text-blue-800 hover:bg-blue-50\",\n        high: \"bg-orange-100 text-orange-800 hover:bg-orange-50\",\n        urgent: \"bg-red-100 text-red-800 hover:bg-red-50\"\n    };\n    // Stage colors for consistent badge coloring (same as modal)\n    const badgeColors = [\n        \"bg-blue-200 text-blue-800\",\n        \"bg-green-200 text-green-800\",\n        \"bg-yellow-200 text-yellow-800\",\n        \"bg-purple-200 text-purple-800\",\n        \"bg-indigo-200 text-indigo-800\",\n        \"bg-pink-200 text-pink-800\",\n        \"bg-orange-200 text-orange-800\",\n        \"bg-red-200 text-red-800\"\n    ];\n    let stageColor = \"bg-gray-200 text-gray-800\";\n    if ((_ticket_pipeline = ticket.pipeline) === null || _ticket_pipeline === void 0 ? void 0 : _ticket_pipeline.stages) {\n        const idx = ticket.pipeline.stages.findIndex((s)=>{\n            var _ticket_currentStage;\n            return s.id === ((_ticket_currentStage = ticket.currentStage) === null || _ticket_currentStage === void 0 ? void 0 : _ticket_currentStage.pipelineStageId);\n        });\n        if (idx !== -1) {\n            stageColor = badgeColors[idx % badgeColors.length];\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"ghost\",\n                        onClick: ()=>router.push(\"/pms/manage_tickets\"),\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Flag_MessageSquare_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, this),\n                            \"Back to Tickets\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg border p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-3\",\n                                            children: ticket.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    className: priorityColors[ticket.priority],\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Flag_MessageSquare_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"mr-1 h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        ticket.priority\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, this),\n                                                currentStage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: stageColor,\n                                                    children: currentStage.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 21\n                                                }, this),\n                                                (ticket.tags || []).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        className: tag.color,\n                                                        variant: \"secondary\",\n                                                        children: tag.tagName || tag.name\n                                                    }, tag.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                                value: activeTab,\n                                onValueChange: setActiveTab,\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                        className: \"grid w-full grid-cols-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"details\",\n                                                children: \"Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"comments\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Flag_MessageSquare_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Comments\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1 text-blue-600 font-bold\",\n                                                        children: [\n                                                            \"(\",\n                                                            commentsCount,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"tags\",\n                                                children: \"Tags\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"activity\",\n                                                children: \"Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"details\",\n                                        className: \"space-y-6 mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold mb-3\",\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 leading-relaxed\",\n                                                            children: ticket.description || \"No description provided\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                                    children: \"Assigned To\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: assignedUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                                                className: \"h-5 w-5\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                                        src: assignedUser.avatar || \" \",\n                                                                                        alt: assignedToDisplay\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 236,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                                        className: \"text-xs\",\n                                                                                        children: assignedUser.username ? assignedUser.username[0].toUpperCase() : \"\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 237,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 235,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: assignedToDisplay\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 241,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: assignedToDisplay\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 244,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                                    children: \"Owner\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-700 text-sm\",\n                                                                    children: ownerDisplay\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        currentStage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                                    children: \"Due Date\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Flag_MessageSquare_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 258,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm \".concat(currentStage.dueAt && new Date(currentStage.dueAt) < new Date() ? \"text-red-600\" : \"\"),\n                                                                            children: currentStage.dueAt ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_18__.format)(new Date(currentStage.dueAt), \"PPP\") : \"No due date\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 259,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                                    children: \"Created\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_18__.format)(new Date(ticket.createdAt), \"PPP\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 268,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                                    children: \"Last Updated\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_18__.format)(new Date(ticket.updatedAt), \"PPP\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 273,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"comments\",\n                                        className: \"space-y-4 mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_comment_section__WEBPACK_IMPORTED_MODULE_9__.CommentSection, {\n                                            ticketId: ticket.id,\n                                            createdBy: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.username) || \"\",\n                                            setCommentsCount: setCommentsCount,\n                                            onCommentsChange: (newComments)=>{\n                                                setTickets((prev)=>prev.map((t)=>t.id === ticket.id ? {\n                                                            ...t,\n                                                            comments: newComments\n                                                        } : t));\n                                            },\n                                            isActive: activeTab === \"comments\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"tags\",\n                                        className: \"space-y-4 mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tag_manager__WEBPACK_IMPORTED_MODULE_10__.TagManager, {\n                                            ticketId: ticket.id,\n                                            assignedTags: ticket.tags,\n                                            onTagsUpdated: handleTagsUpdated,\n                                            onTagsChange: (newTags)=>{\n                                                setTickets((prev)=>prev.map((t)=>t.id === ticket.id ? {\n                                                            ...t,\n                                                            tags: newTags\n                                                        } : t));\n                                            },\n                                            createdBy: ticket.createdBy || \"\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"activity\",\n                                        className: \"space-y-4 mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_activity_section__WEBPACK_IMPORTED_MODULE_12__.ActivitySection, {\n                                            ticketId: ticket.id\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\[id]\\\\page.tsx\",\n        lineNumber: 175,\n        columnNumber: 5\n    }, this);\n} /* eslint-disable */ \n_s(TicketDetailPage, \"UBA1cHDuIgAw9AmzNQsTCGeKoG0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TicketDetailPage;\nfunction oo_cm() {\n    try {\n        return (0, eval)(\"globalThis._console_ninja\") || (0, eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x24f63b=_0x52e5;(function(_0x534b28,_0x1567f0){var _0x519f45=_0x52e5,_0x1884c1=_0x534b28();while(!![]){try{var _0x31c5f0=parseInt(_0x519f45(0x94))/0x1+parseInt(_0x519f45(0xc9))/0x2*(parseInt(_0x519f45(0xaa))/0x3)+-parseInt(_0x519f45(0xee))/0x4+-parseInt(_0x519f45(0x102))/0x5+-parseInt(_0x519f45(0x13d))/0x6*(-parseInt(_0x519f45(0x15c))/0x7)+parseInt(_0x519f45(0xdc))/0x8*(parseInt(_0x519f45(0x9c))/0x9)+parseInt(_0x519f45(0x16d))/0xa;if(_0x31c5f0===_0x1567f0)break;else _0x1884c1['push'](_0x1884c1['shift']());}catch(_0x3e2f53){_0x1884c1['push'](_0x1884c1['shift']());}}}(_0x4f1d,0x1d8f6));var G=Object[_0x24f63b(0xcc)],V=Object['defineProperty'],ee=Object[_0x24f63b(0xa0)],te=Object['getOwnPropertyNames'],ne=Object[_0x24f63b(0x8d)],re=Object[_0x24f63b(0x179)][_0x24f63b(0x14f)],ie=(_0x5ad68c,_0x54a116,_0x586abe,_0x2f1d39)=>{var _0xbcf9b6=_0x24f63b;if(_0x54a116&&typeof _0x54a116==_0xbcf9b6(0x145)||typeof _0x54a116=='function'){for(let _0x3d18bd of te(_0x54a116))!re[_0xbcf9b6(0x138)](_0x5ad68c,_0x3d18bd)&&_0x3d18bd!==_0x586abe&&V(_0x5ad68c,_0x3d18bd,{'get':()=>_0x54a116[_0x3d18bd],'enumerable':!(_0x2f1d39=ee(_0x54a116,_0x3d18bd))||_0x2f1d39[_0xbcf9b6(0x137)]});}return _0x5ad68c;},j=(_0x45abce,_0x2df7c2,_0x3c1471)=>(_0x3c1471=_0x45abce!=null?G(ne(_0x45abce)):{},ie(_0x2df7c2||!_0x45abce||!_0x45abce['__es'+'Module']?V(_0x3c1471,_0x24f63b(0xca),{'value':_0x45abce,'enumerable':!0x0}):_0x3c1471,_0x45abce)),q=class{constructor(_0xb179d1,_0xccf6fa,_0x82c744,_0x14fdb2,_0x18deaf,_0x147577){var _0x37f9bf=_0x24f63b,_0x2130de,_0x29e2d7,_0x52a5da,_0x20a568;this['global']=_0xb179d1,this[_0x37f9bf(0xc0)]=_0xccf6fa,this[_0x37f9bf(0x13e)]=_0x82c744,this['nodeModules']=_0x14fdb2,this[_0x37f9bf(0x15f)]=_0x18deaf,this['eventReceivedCallback']=_0x147577,this[_0x37f9bf(0xa4)]=!0x0,this[_0x37f9bf(0xc7)]=!0x0,this[_0x37f9bf(0xf1)]=!0x1,this[_0x37f9bf(0x10e)]=!0x1,this[_0x37f9bf(0x129)]=((_0x29e2d7=(_0x2130de=_0xb179d1[_0x37f9bf(0x16e)])==null?void 0x0:_0x2130de[_0x37f9bf(0x11a)])==null?void 0x0:_0x29e2d7['NEXT_RUNTIME'])===_0x37f9bf(0x169),this[_0x37f9bf(0xb7)]=!((_0x20a568=(_0x52a5da=this['global'][_0x37f9bf(0x16e)])==null?void 0x0:_0x52a5da[_0x37f9bf(0xbc)])!=null&&_0x20a568[_0x37f9bf(0x106)])&&!this['_inNextEdge'],this[_0x37f9bf(0x116)]=null,this[_0x37f9bf(0x124)]=0x0,this['_maxConnectAttemptCount']=0x14,this[_0x37f9bf(0x15d)]=_0x37f9bf(0x175),this[_0x37f9bf(0x82)]=(this[_0x37f9bf(0xb7)]?_0x37f9bf(0xff):_0x37f9bf(0xb0))+this['_webSocketErrorDocsLink'];}async[_0x24f63b(0x8a)](){var _0x209cf0=_0x24f63b,_0x215408,_0x2e3df7;if(this['_WebSocketClass'])return this['_WebSocketClass'];let _0x9bf141;if(this['_inBrowser']||this['_inNextEdge'])_0x9bf141=this[_0x209cf0(0xe9)][_0x209cf0(0xb8)];else{if((_0x215408=this[_0x209cf0(0xe9)]['process'])!=null&&_0x215408[_0x209cf0(0x135)])_0x9bf141=(_0x2e3df7=this['global']['process'])==null?void 0x0:_0x2e3df7[_0x209cf0(0x135)];else try{let _0x2387ca=await import(_0x209cf0(0x97));_0x9bf141=(await import((await import('url'))[_0x209cf0(0xa2)](_0x2387ca['join'](this[_0x209cf0(0x125)],_0x209cf0(0x100)))[_0x209cf0(0xba)]()))[_0x209cf0(0xca)];}catch{try{_0x9bf141=require(require('path')[_0x209cf0(0x171)](this[_0x209cf0(0x125)],'ws'));}catch{throw new Error('failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket');}}}return this[_0x209cf0(0x116)]=_0x9bf141,_0x9bf141;}[_0x24f63b(0xc8)](){var _0x3cfedc=_0x24f63b;this['_connecting']||this[_0x3cfedc(0xf1)]||this[_0x3cfedc(0x124)]>=this[_0x3cfedc(0x118)]||(this[_0x3cfedc(0xc7)]=!0x1,this['_connecting']=!0x0,this['_connectAttemptCount']++,this[_0x3cfedc(0x148)]=new Promise((_0x52f69e,_0x5666f6)=>{var _0x47235c=_0x3cfedc;this['getWebSocketClass']()[_0x47235c(0xa5)](_0x48968f=>{var _0x572f48=_0x47235c;let _0x1a51ab=new _0x48968f(_0x572f48(0x9e)+(!this[_0x572f48(0xb7)]&&this[_0x572f48(0x15f)]?_0x572f48(0x115):this[_0x572f48(0xc0)])+':'+this[_0x572f48(0x13e)]);_0x1a51ab[_0x572f48(0xd4)]=()=>{var _0x438606=_0x572f48;this['_allowedToSend']=!0x1,this[_0x438606(0xf3)](_0x1a51ab),this[_0x438606(0x12d)](),_0x5666f6(new Error(_0x438606(0xbf)));},_0x1a51ab[_0x572f48(0x170)]=()=>{var _0x284d3c=_0x572f48;this[_0x284d3c(0xb7)]||_0x1a51ab['_socket']&&_0x1a51ab[_0x284d3c(0x8c)][_0x284d3c(0xf7)]&&_0x1a51ab[_0x284d3c(0x8c)]['unref'](),_0x52f69e(_0x1a51ab);},_0x1a51ab[_0x572f48(0x14e)]=()=>{var _0x250996=_0x572f48;this[_0x250996(0xc7)]=!0x0,this[_0x250996(0xf3)](_0x1a51ab),this[_0x250996(0x12d)]();},_0x1a51ab[_0x572f48(0xb2)]=_0x23850c=>{var _0x3542b9=_0x572f48;try{if(!(_0x23850c!=null&&_0x23850c['data'])||!this[_0x3542b9(0x13f)])return;let _0x284572=JSON[_0x3542b9(0x12a)](_0x23850c[_0x3542b9(0xa9)]);this[_0x3542b9(0x13f)](_0x284572[_0x3542b9(0x9a)],_0x284572[_0x3542b9(0x11c)],this[_0x3542b9(0xe9)],this[_0x3542b9(0xb7)]);}catch{}};})[_0x47235c(0xa5)](_0x129b2a=>(this['_connected']=!0x0,this[_0x47235c(0x10e)]=!0x1,this[_0x47235c(0xc7)]=!0x1,this['_allowedToSend']=!0x0,this[_0x47235c(0x124)]=0x0,_0x129b2a))[_0x47235c(0xa6)](_0x3152b9=>(this['_connected']=!0x1,this[_0x47235c(0x10e)]=!0x1,console[_0x47235c(0x112)](_0x47235c(0x134)+this[_0x47235c(0x15d)]),_0x5666f6(new Error(_0x47235c(0x121)+(_0x3152b9&&_0x3152b9[_0x47235c(0x128)])))));}));}[_0x24f63b(0xf3)](_0x5c9f96){var _0x395e4b=_0x24f63b;this[_0x395e4b(0xf1)]=!0x1,this[_0x395e4b(0x10e)]=!0x1;try{_0x5c9f96[_0x395e4b(0x14e)]=null,_0x5c9f96[_0x395e4b(0xd4)]=null,_0x5c9f96[_0x395e4b(0x170)]=null;}catch{}try{_0x5c9f96[_0x395e4b(0x151)]<0x2&&_0x5c9f96[_0x395e4b(0x10b)]();}catch{}}[_0x24f63b(0x12d)](){var _0x39cf4a=_0x24f63b;clearTimeout(this[_0x39cf4a(0x16c)]),!(this['_connectAttemptCount']>=this['_maxConnectAttemptCount'])&&(this[_0x39cf4a(0x16c)]=setTimeout(()=>{var _0x25773e=_0x39cf4a,_0x58705d;this[_0x25773e(0xf1)]||this[_0x25773e(0x10e)]||(this[_0x25773e(0xc8)](),(_0x58705d=this[_0x25773e(0x148)])==null||_0x58705d['catch'](()=>this[_0x25773e(0x12d)]()));},0x1f4),this[_0x39cf4a(0x16c)]['unref']&&this[_0x39cf4a(0x16c)][_0x39cf4a(0xf7)]());}async[_0x24f63b(0xfe)](_0x4171a2){var _0x2b8b82=_0x24f63b;try{if(!this[_0x2b8b82(0xa4)])return;this[_0x2b8b82(0xc7)]&&this[_0x2b8b82(0xc8)](),(await this['_ws'])[_0x2b8b82(0xfe)](JSON['stringify'](_0x4171a2));}catch(_0x57ef45){this[_0x2b8b82(0x105)]?console[_0x2b8b82(0x112)](this[_0x2b8b82(0x82)]+':\\\\x20'+(_0x57ef45&&_0x57ef45[_0x2b8b82(0x128)])):(this[_0x2b8b82(0x105)]=!0x0,console[_0x2b8b82(0x112)](this[_0x2b8b82(0x82)]+':\\\\x20'+(_0x57ef45&&_0x57ef45[_0x2b8b82(0x128)]),_0x4171a2)),this[_0x2b8b82(0xa4)]=!0x1,this[_0x2b8b82(0x12d)]();}}};function H(_0xddb998,_0x2a7be2,_0x31f146,_0x152747,_0x55df57,_0x5a8ea1,_0x84ada,_0x52d717=oe){var _0x3e9754=_0x24f63b;let _0x1066a1=_0x31f146[_0x3e9754(0x141)](',')[_0x3e9754(0xbe)](_0x8ba870=>{var _0x3bb906=_0x3e9754,_0x35166e,_0xd6118a,_0x2318b6,_0x4a529f;try{if(!_0xddb998[_0x3bb906(0xf4)]){let _0x3b938d=((_0xd6118a=(_0x35166e=_0xddb998['process'])==null?void 0x0:_0x35166e[_0x3bb906(0xbc)])==null?void 0x0:_0xd6118a[_0x3bb906(0x106)])||((_0x4a529f=(_0x2318b6=_0xddb998['process'])==null?void 0x0:_0x2318b6['env'])==null?void 0x0:_0x4a529f[_0x3bb906(0x10c)])===_0x3bb906(0x169);(_0x55df57===_0x3bb906(0x88)||_0x55df57===_0x3bb906(0xf2)||_0x55df57===_0x3bb906(0x157)||_0x55df57===_0x3bb906(0xd5))&&(_0x55df57+=_0x3b938d?_0x3bb906(0x149):_0x3bb906(0xc1)),_0xddb998[_0x3bb906(0xf4)]={'id':+new Date(),'tool':_0x55df57},_0x84ada&&_0x55df57&&!_0x3b938d&&console[_0x3bb906(0x160)](_0x3bb906(0x152)+(_0x55df57['charAt'](0x0)[_0x3bb906(0x11d)]()+_0x55df57[_0x3bb906(0xd6)](0x1))+',','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)',_0x3bb906(0xdb));}let _0x5c4515=new q(_0xddb998,_0x2a7be2,_0x8ba870,_0x152747,_0x5a8ea1,_0x52d717);return _0x5c4515[_0x3bb906(0xfe)][_0x3bb906(0x16f)](_0x5c4515);}catch(_0xb1b569){return console[_0x3bb906(0x112)]('logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host',_0xb1b569&&_0xb1b569[_0x3bb906(0x128)]),()=>{};}});return _0x559b7b=>_0x1066a1[_0x3e9754(0xe0)](_0x53f86f=>_0x53f86f(_0x559b7b));}function oe(_0x379c5d,_0x7f7fe,_0xf2dc6a,_0x182b60){var _0x5e75c1=_0x24f63b;_0x182b60&&_0x379c5d===_0x5e75c1(0xfd)&&_0xf2dc6a[_0x5e75c1(0xd7)][_0x5e75c1(0xfd)]();}function B(_0x15ad40){var _0x44db1e=_0x24f63b,_0x58c57e,_0x4d6388;let _0x2f71d4=function(_0x225c53,_0x5bff95){return _0x5bff95-_0x225c53;},_0x2b9183;if(_0x15ad40['performance'])_0x2b9183=function(){var _0x58b055=_0x52e5;return _0x15ad40[_0x58b055(0x81)][_0x58b055(0x123)]();};else{if(_0x15ad40[_0x44db1e(0x16e)]&&_0x15ad40[_0x44db1e(0x16e)]['hrtime']&&((_0x4d6388=(_0x58c57e=_0x15ad40[_0x44db1e(0x16e)])==null?void 0x0:_0x58c57e[_0x44db1e(0x11a)])==null?void 0x0:_0x4d6388[_0x44db1e(0x10c)])!==_0x44db1e(0x169))_0x2b9183=function(){var _0x3dbed7=_0x44db1e;return _0x15ad40[_0x3dbed7(0x16e)][_0x3dbed7(0xf6)]();},_0x2f71d4=function(_0x324f8e,_0x37d9df){return 0x3e8*(_0x37d9df[0x0]-_0x324f8e[0x0])+(_0x37d9df[0x1]-_0x324f8e[0x1])/0xf4240;};else try{let {performance:_0x3a3c47}=require(_0x44db1e(0x113));_0x2b9183=function(){var _0x4d3499=_0x44db1e;return _0x3a3c47[_0x4d3499(0x123)]();};}catch{_0x2b9183=function(){return+new Date();};}}return{'elapsed':_0x2f71d4,'timeStamp':_0x2b9183,'now':()=>Date[_0x44db1e(0x123)]()};}function X(_0x33b074,_0x5f43b4,_0x2566da){var _0x2c029d=_0x24f63b,_0x4e0c6d,_0x7773c2,_0x88662c,_0x102a6a,_0x4a7930;if(_0x33b074['_consoleNinjaAllowedToStart']!==void 0x0)return _0x33b074['_consoleNinjaAllowedToStart'];let _0x5503c5=((_0x7773c2=(_0x4e0c6d=_0x33b074[_0x2c029d(0x16e)])==null?void 0x0:_0x4e0c6d[_0x2c029d(0xbc)])==null?void 0x0:_0x7773c2[_0x2c029d(0x106)])||((_0x102a6a=(_0x88662c=_0x33b074[_0x2c029d(0x16e)])==null?void 0x0:_0x88662c[_0x2c029d(0x11a)])==null?void 0x0:_0x102a6a[_0x2c029d(0x10c)])===_0x2c029d(0x169);function _0x3662d5(_0x366436){var _0x47395b=_0x2c029d;if(_0x366436[_0x47395b(0xea)]('/')&&_0x366436['endsWith']('/')){let _0x195b84=new RegExp(_0x366436[_0x47395b(0x139)](0x1,-0x1));return _0x46cd10=>_0x195b84[_0x47395b(0x177)](_0x46cd10);}else{if(_0x366436[_0x47395b(0xc3)]('*')||_0x366436[_0x47395b(0xc3)]('?')){let _0x8dce08=new RegExp('^'+_0x366436[_0x47395b(0xd2)](/\\\\./g,String[_0x47395b(0x84)](0x5c)+'.')[_0x47395b(0xd2)](/\\\\*/g,'.*')[_0x47395b(0xd2)](/\\\\?/g,'.')+String['fromCharCode'](0x24));return _0x47a4bc=>_0x8dce08[_0x47395b(0x177)](_0x47a4bc);}else return _0x575e99=>_0x575e99===_0x366436;}}let _0x11ae56=_0x5f43b4[_0x2c029d(0xbe)](_0x3662d5);return _0x33b074[_0x2c029d(0xe7)]=_0x5503c5||!_0x5f43b4,!_0x33b074[_0x2c029d(0xe7)]&&((_0x4a7930=_0x33b074[_0x2c029d(0xd7)])==null?void 0x0:_0x4a7930[_0x2c029d(0xda)])&&(_0x33b074[_0x2c029d(0xe7)]=_0x11ae56[_0x2c029d(0x14a)](_0x5046f4=>_0x5046f4(_0x33b074['location'][_0x2c029d(0xda)]))),_0x33b074[_0x2c029d(0xe7)];}function _0x52e5(_0x220dab,_0x367b56){var _0x4f1def=_0x4f1d();return _0x52e5=function(_0x52e579,_0x310489){_0x52e579=_0x52e579-0x81;var _0x2337a4=_0x4f1def[_0x52e579];return _0x2337a4;},_0x52e5(_0x220dab,_0x367b56);}function J(_0x49fb52,_0x7016a8,_0x260c36,_0x176c7b){var _0x332963=_0x24f63b;_0x49fb52=_0x49fb52,_0x7016a8=_0x7016a8,_0x260c36=_0x260c36,_0x176c7b=_0x176c7b;let _0x3f6214=B(_0x49fb52),_0x1525d9=_0x3f6214[_0x332963(0x158)],_0x4b914c=_0x3f6214[_0x332963(0x156)];class _0x25ba71{constructor(){var _0x1560c3=_0x332963;this[_0x1560c3(0x117)]=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this['_numberRegExp']=/^(0|[1-9][0-9]*)$/,this[_0x1560c3(0xc2)]=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x1560c3(0xa1)]=_0x49fb52[_0x1560c3(0x173)],this[_0x1560c3(0xac)]=_0x49fb52['HTMLAllCollection'],this['_getOwnPropertyDescriptor']=Object[_0x1560c3(0xa0)],this[_0x1560c3(0x131)]=Object[_0x1560c3(0xe3)],this[_0x1560c3(0xce)]=_0x49fb52[_0x1560c3(0x103)],this['_regExpToString']=RegExp[_0x1560c3(0x179)]['toString'],this[_0x1560c3(0xf0)]=Date['prototype'][_0x1560c3(0xba)];}[_0x332963(0x132)](_0x5718db,_0x36c6dc,_0x434e7e,_0x37985c){var _0x602be6=_0x332963,_0x2599ff=this,_0x407704=_0x434e7e[_0x602be6(0xcf)];function _0x4c36ed(_0x2a6d02,_0xb3eba5,_0x1d63ef){var _0x2cba0f=_0x602be6;_0xb3eba5['type']=_0x2cba0f(0x12f),_0xb3eba5[_0x2cba0f(0x122)]=_0x2a6d02[_0x2cba0f(0x128)],_0x1733da=_0x1d63ef[_0x2cba0f(0x106)][_0x2cba0f(0x86)],_0x1d63ef[_0x2cba0f(0x106)][_0x2cba0f(0x86)]=_0xb3eba5,_0x2599ff[_0x2cba0f(0xcb)](_0xb3eba5,_0x1d63ef);}let _0x5a03b2;_0x49fb52[_0x602be6(0x153)]&&(_0x5a03b2=_0x49fb52[_0x602be6(0x153)]['error'],_0x5a03b2&&(_0x49fb52[_0x602be6(0x153)][_0x602be6(0x122)]=function(){}));try{try{_0x434e7e[_0x602be6(0xfb)]++,_0x434e7e[_0x602be6(0xcf)]&&_0x434e7e['autoExpandPreviousObjects']['push'](_0x36c6dc);var _0x14fee5,_0x347743,_0x48074c,_0x3571d7,_0x2a6dff=[],_0x2c12be=[],_0x3bfbae,_0x297a15=this[_0x602be6(0x8f)](_0x36c6dc),_0x5930d9=_0x297a15==='array',_0x1a53e0=!0x1,_0x60bf72=_0x297a15==='function',_0x23f6ec=this[_0x602be6(0x165)](_0x297a15),_0x3a7e13=this[_0x602be6(0xef)](_0x297a15),_0x561396=_0x23f6ec||_0x3a7e13,_0x542ed6={},_0x1487f5=0x0,_0x1d988d=!0x1,_0x1733da,_0x39bba3=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x434e7e[_0x602be6(0xc4)]){if(_0x5930d9){if(_0x347743=_0x36c6dc[_0x602be6(0x147)],_0x347743>_0x434e7e[_0x602be6(0x8e)]){for(_0x48074c=0x0,_0x3571d7=_0x434e7e[_0x602be6(0x8e)],_0x14fee5=_0x48074c;_0x14fee5<_0x3571d7;_0x14fee5++)_0x2c12be['push'](_0x2599ff[_0x602be6(0xeb)](_0x2a6dff,_0x36c6dc,_0x297a15,_0x14fee5,_0x434e7e));_0x5718db[_0x602be6(0x108)]=!0x0;}else{for(_0x48074c=0x0,_0x3571d7=_0x347743,_0x14fee5=_0x48074c;_0x14fee5<_0x3571d7;_0x14fee5++)_0x2c12be['push'](_0x2599ff['_addProperty'](_0x2a6dff,_0x36c6dc,_0x297a15,_0x14fee5,_0x434e7e));}_0x434e7e['autoExpandPropertyCount']+=_0x2c12be[_0x602be6(0x147)];}if(!(_0x297a15===_0x602be6(0x178)||_0x297a15===_0x602be6(0x173))&&!_0x23f6ec&&_0x297a15!=='String'&&_0x297a15!==_0x602be6(0xc5)&&_0x297a15!==_0x602be6(0x9b)){var _0x584571=_0x37985c[_0x602be6(0x8b)]||_0x434e7e[_0x602be6(0x8b)];if(this[_0x602be6(0x16b)](_0x36c6dc)?(_0x14fee5=0x0,_0x36c6dc[_0x602be6(0xe0)](function(_0x25d373){var _0x5d592f=_0x602be6;if(_0x1487f5++,_0x434e7e['autoExpandPropertyCount']++,_0x1487f5>_0x584571){_0x1d988d=!0x0;return;}if(!_0x434e7e['isExpressionToEvaluate']&&_0x434e7e[_0x5d592f(0xcf)]&&_0x434e7e['autoExpandPropertyCount']>_0x434e7e[_0x5d592f(0xb1)]){_0x1d988d=!0x0;return;}_0x2c12be[_0x5d592f(0xa8)](_0x2599ff[_0x5d592f(0xeb)](_0x2a6dff,_0x36c6dc,_0x5d592f(0x136),_0x14fee5++,_0x434e7e,function(_0x255c9a){return function(){return _0x255c9a;};}(_0x25d373)));})):this[_0x602be6(0x14c)](_0x36c6dc)&&_0x36c6dc['forEach'](function(_0x1fa0be,_0x144558){var _0x46719b=_0x602be6;if(_0x1487f5++,_0x434e7e[_0x46719b(0xad)]++,_0x1487f5>_0x584571){_0x1d988d=!0x0;return;}if(!_0x434e7e[_0x46719b(0xe8)]&&_0x434e7e[_0x46719b(0xcf)]&&_0x434e7e[_0x46719b(0xad)]>_0x434e7e['autoExpandLimit']){_0x1d988d=!0x0;return;}var _0x3484a0=_0x144558[_0x46719b(0xba)]();_0x3484a0[_0x46719b(0x147)]>0x64&&(_0x3484a0=_0x3484a0[_0x46719b(0x139)](0x0,0x64)+_0x46719b(0xae)),_0x2c12be['push'](_0x2599ff[_0x46719b(0xeb)](_0x2a6dff,_0x36c6dc,_0x46719b(0x110),_0x3484a0,_0x434e7e,function(_0x4d5f7a){return function(){return _0x4d5f7a;};}(_0x1fa0be)));}),!_0x1a53e0){try{for(_0x3bfbae in _0x36c6dc)if(!(_0x5930d9&&_0x39bba3[_0x602be6(0x177)](_0x3bfbae))&&!this['_blacklistedProperty'](_0x36c6dc,_0x3bfbae,_0x434e7e)){if(_0x1487f5++,_0x434e7e[_0x602be6(0xad)]++,_0x1487f5>_0x584571){_0x1d988d=!0x0;break;}if(!_0x434e7e[_0x602be6(0xe8)]&&_0x434e7e['autoExpand']&&_0x434e7e[_0x602be6(0xad)]>_0x434e7e['autoExpandLimit']){_0x1d988d=!0x0;break;}_0x2c12be['push'](_0x2599ff[_0x602be6(0x155)](_0x2a6dff,_0x542ed6,_0x36c6dc,_0x297a15,_0x3bfbae,_0x434e7e));}}catch{}if(_0x542ed6[_0x602be6(0xc6)]=!0x0,_0x60bf72&&(_0x542ed6['_p_name']=!0x0),!_0x1d988d){var _0x2a1f31=[][_0x602be6(0xd9)](this['_getOwnPropertyNames'](_0x36c6dc))[_0x602be6(0xd9)](this[_0x602be6(0xb6)](_0x36c6dc));for(_0x14fee5=0x0,_0x347743=_0x2a1f31[_0x602be6(0x147)];_0x14fee5<_0x347743;_0x14fee5++)if(_0x3bfbae=_0x2a1f31[_0x14fee5],!(_0x5930d9&&_0x39bba3['test'](_0x3bfbae[_0x602be6(0xba)]()))&&!this[_0x602be6(0x143)](_0x36c6dc,_0x3bfbae,_0x434e7e)&&!_0x542ed6[_0x602be6(0xb5)+_0x3bfbae['toString']()]){if(_0x1487f5++,_0x434e7e[_0x602be6(0xad)]++,_0x1487f5>_0x584571){_0x1d988d=!0x0;break;}if(!_0x434e7e['isExpressionToEvaluate']&&_0x434e7e[_0x602be6(0xcf)]&&_0x434e7e['autoExpandPropertyCount']>_0x434e7e['autoExpandLimit']){_0x1d988d=!0x0;break;}_0x2c12be[_0x602be6(0xa8)](_0x2599ff[_0x602be6(0x155)](_0x2a6dff,_0x542ed6,_0x36c6dc,_0x297a15,_0x3bfbae,_0x434e7e));}}}}}if(_0x5718db[_0x602be6(0x144)]=_0x297a15,_0x561396?(_0x5718db[_0x602be6(0x133)]=_0x36c6dc[_0x602be6(0x92)](),this['_capIfString'](_0x297a15,_0x5718db,_0x434e7e,_0x37985c)):_0x297a15===_0x602be6(0xdf)?_0x5718db[_0x602be6(0x133)]=this[_0x602be6(0xf0)][_0x602be6(0x138)](_0x36c6dc):_0x297a15===_0x602be6(0x9b)?_0x5718db[_0x602be6(0x133)]=_0x36c6dc[_0x602be6(0xba)]():_0x297a15===_0x602be6(0x142)?_0x5718db[_0x602be6(0x133)]=this[_0x602be6(0xcd)][_0x602be6(0x138)](_0x36c6dc):_0x297a15===_0x602be6(0x140)&&this[_0x602be6(0xce)]?_0x5718db[_0x602be6(0x133)]=this[_0x602be6(0xce)][_0x602be6(0x179)]['toString'][_0x602be6(0x138)](_0x36c6dc):!_0x434e7e['depth']&&!(_0x297a15===_0x602be6(0x178)||_0x297a15===_0x602be6(0x173))&&(delete _0x5718db[_0x602be6(0x133)],_0x5718db[_0x602be6(0x12e)]=!0x0),_0x1d988d&&(_0x5718db['cappedProps']=!0x0),_0x1733da=_0x434e7e[_0x602be6(0x106)]['current'],_0x434e7e[_0x602be6(0x106)][_0x602be6(0x86)]=_0x5718db,this['_treeNodePropertiesBeforeFullValue'](_0x5718db,_0x434e7e),_0x2c12be[_0x602be6(0x147)]){for(_0x14fee5=0x0,_0x347743=_0x2c12be[_0x602be6(0x147)];_0x14fee5<_0x347743;_0x14fee5++)_0x2c12be[_0x14fee5](_0x14fee5);}_0x2a6dff['length']&&(_0x5718db[_0x602be6(0x8b)]=_0x2a6dff);}catch(_0x113a79){_0x4c36ed(_0x113a79,_0x5718db,_0x434e7e);}this[_0x602be6(0xa3)](_0x36c6dc,_0x5718db),this['_treeNodePropertiesAfterFullValue'](_0x5718db,_0x434e7e),_0x434e7e['node'][_0x602be6(0x86)]=_0x1733da,_0x434e7e[_0x602be6(0xfb)]--,_0x434e7e[_0x602be6(0xcf)]=_0x407704,_0x434e7e[_0x602be6(0xcf)]&&_0x434e7e[_0x602be6(0x11b)]['pop']();}finally{_0x5a03b2&&(_0x49fb52[_0x602be6(0x153)]['error']=_0x5a03b2);}return _0x5718db;}['_getOwnPropertySymbols'](_0x5384f9){var _0x309726=_0x332963;return Object[_0x309726(0x126)]?Object['getOwnPropertySymbols'](_0x5384f9):[];}[_0x332963(0x16b)](_0x5c547c){var _0x35f5e6=_0x332963;return!!(_0x5c547c&&_0x49fb52[_0x35f5e6(0x136)]&&this[_0x35f5e6(0x10d)](_0x5c547c)==='[object\\\\x20Set]'&&_0x5c547c[_0x35f5e6(0xe0)]);}[_0x332963(0x143)](_0x245460,_0x437d65,_0x21eeae){var _0x376999=_0x332963;return _0x21eeae[_0x376999(0xfa)]?typeof _0x245460[_0x437d65]==_0x376999(0x95):!0x1;}[_0x332963(0x8f)](_0x46b1a0){var _0x3de89e=_0x332963,_0xdedd36='';return _0xdedd36=typeof _0x46b1a0,_0xdedd36==='object'?this['_objectToString'](_0x46b1a0)==='[object\\\\x20Array]'?_0xdedd36=_0x3de89e(0x114):this['_objectToString'](_0x46b1a0)===_0x3de89e(0x83)?_0xdedd36=_0x3de89e(0xdf):this[_0x3de89e(0x10d)](_0x46b1a0)===_0x3de89e(0x174)?_0xdedd36=_0x3de89e(0x9b):_0x46b1a0===null?_0xdedd36='null':_0x46b1a0['constructor']&&(_0xdedd36=_0x46b1a0[_0x3de89e(0x89)][_0x3de89e(0x150)]||_0xdedd36):_0xdedd36==='undefined'&&this[_0x3de89e(0xac)]&&_0x46b1a0 instanceof this[_0x3de89e(0xac)]&&(_0xdedd36='HTMLAllCollection'),_0xdedd36;}['_objectToString'](_0x19ace1){var _0x39f229=_0x332963;return Object[_0x39f229(0x179)][_0x39f229(0xba)][_0x39f229(0x138)](_0x19ace1);}[_0x332963(0x165)](_0x3eebc7){var _0x16c3c2=_0x332963;return _0x3eebc7==='boolean'||_0x3eebc7===_0x16c3c2(0xa7)||_0x3eebc7===_0x16c3c2(0x14b);}[_0x332963(0xef)](_0x475bed){var _0x520b17=_0x332963;return _0x475bed===_0x520b17(0xec)||_0x475bed===_0x520b17(0x10f)||_0x475bed===_0x520b17(0x107);}[_0x332963(0xeb)](_0x2eeaeb,_0x306076,_0x1de3aa,_0x5d8f33,_0x50b043,_0x244a39){var _0x59d500=this;return function(_0xe3807){var _0x457ee7=_0x52e5,_0x201409=_0x50b043['node'][_0x457ee7(0x86)],_0x57bc99=_0x50b043[_0x457ee7(0x106)][_0x457ee7(0x99)],_0x474e69=_0x50b043[_0x457ee7(0x106)][_0x457ee7(0xab)];_0x50b043[_0x457ee7(0x106)][_0x457ee7(0xab)]=_0x201409,_0x50b043[_0x457ee7(0x106)][_0x457ee7(0x99)]=typeof _0x5d8f33==_0x457ee7(0x14b)?_0x5d8f33:_0xe3807,_0x2eeaeb[_0x457ee7(0xa8)](_0x59d500[_0x457ee7(0xaf)](_0x306076,_0x1de3aa,_0x5d8f33,_0x50b043,_0x244a39)),_0x50b043[_0x457ee7(0x106)][_0x457ee7(0xab)]=_0x474e69,_0x50b043['node'][_0x457ee7(0x99)]=_0x57bc99;};}['_addObjectProperty'](_0x5905fe,_0x1c1b38,_0x324bfd,_0x295211,_0x3420e9,_0x29125b,_0x128ff7){var _0x38fc51=this;return _0x1c1b38['_p_'+_0x3420e9['toString']()]=!0x0,function(_0x4d6bb0){var _0x15590f=_0x52e5,_0x35e21e=_0x29125b[_0x15590f(0x106)]['current'],_0x477cd5=_0x29125b['node'][_0x15590f(0x99)],_0x109573=_0x29125b['node']['parent'];_0x29125b[_0x15590f(0x106)]['parent']=_0x35e21e,_0x29125b[_0x15590f(0x106)][_0x15590f(0x99)]=_0x4d6bb0,_0x5905fe[_0x15590f(0xa8)](_0x38fc51['_property'](_0x324bfd,_0x295211,_0x3420e9,_0x29125b,_0x128ff7)),_0x29125b[_0x15590f(0x106)][_0x15590f(0xab)]=_0x109573,_0x29125b[_0x15590f(0x106)][_0x15590f(0x99)]=_0x477cd5;};}[_0x332963(0xaf)](_0x445beb,_0x1caecc,_0x5c45d7,_0x3a10e1,_0xaa0ad1){var _0x441229=_0x332963,_0x133087=this;_0xaa0ad1||(_0xaa0ad1=function(_0x2eeaf3,_0x293fe0){return _0x2eeaf3[_0x293fe0];});var _0x54b5c6=_0x5c45d7[_0x441229(0xba)](),_0x1027a2=_0x3a10e1[_0x441229(0xb4)]||{},_0x26801b=_0x3a10e1[_0x441229(0xc4)],_0x4bc711=_0x3a10e1['isExpressionToEvaluate'];try{var _0x1b3b34=this[_0x441229(0x14c)](_0x445beb),_0x2e9d8d=_0x54b5c6;_0x1b3b34&&_0x2e9d8d[0x0]==='\\\\x27'&&(_0x2e9d8d=_0x2e9d8d[_0x441229(0xd6)](0x1,_0x2e9d8d[_0x441229(0x147)]-0x2));var _0x58880c=_0x3a10e1[_0x441229(0xb4)]=_0x1027a2[_0x441229(0xb5)+_0x2e9d8d];_0x58880c&&(_0x3a10e1[_0x441229(0xc4)]=_0x3a10e1[_0x441229(0xc4)]+0x1),_0x3a10e1[_0x441229(0xe8)]=!!_0x58880c;var _0x1d2183=typeof _0x5c45d7=='symbol',_0x5387e5={'name':_0x1d2183||_0x1b3b34?_0x54b5c6:this[_0x441229(0xe4)](_0x54b5c6)};if(_0x1d2183&&(_0x5387e5[_0x441229(0x140)]=!0x0),!(_0x1caecc===_0x441229(0x114)||_0x1caecc==='Error')){var _0xc0f71c=this['_getOwnPropertyDescriptor'](_0x445beb,_0x5c45d7);if(_0xc0f71c&&(_0xc0f71c[_0x441229(0xe5)]&&(_0x5387e5['setter']=!0x0),_0xc0f71c[_0x441229(0xd3)]&&!_0x58880c&&!_0x3a10e1[_0x441229(0xe1)]))return _0x5387e5[_0x441229(0x96)]=!0x0,this['_processTreeNodeResult'](_0x5387e5,_0x3a10e1),_0x5387e5;}var _0xc66542;try{_0xc66542=_0xaa0ad1(_0x445beb,_0x5c45d7);}catch(_0x2f1ccc){return _0x5387e5={'name':_0x54b5c6,'type':'unknown','error':_0x2f1ccc['message']},this['_processTreeNodeResult'](_0x5387e5,_0x3a10e1),_0x5387e5;}var _0x444339=this[_0x441229(0x8f)](_0xc66542),_0x1aa19f=this[_0x441229(0x165)](_0x444339);if(_0x5387e5[_0x441229(0x144)]=_0x444339,_0x1aa19f)this[_0x441229(0x15e)](_0x5387e5,_0x3a10e1,_0xc66542,function(){var _0x822493=_0x441229;_0x5387e5[_0x822493(0x133)]=_0xc66542['valueOf'](),!_0x58880c&&_0x133087[_0x822493(0x127)](_0x444339,_0x5387e5,_0x3a10e1,{});});else{var _0x149e19=_0x3a10e1[_0x441229(0xcf)]&&_0x3a10e1[_0x441229(0xfb)]<_0x3a10e1[_0x441229(0xd0)]&&_0x3a10e1[_0x441229(0x11b)]['indexOf'](_0xc66542)<0x0&&_0x444339!==_0x441229(0x95)&&_0x3a10e1[_0x441229(0xad)]<_0x3a10e1[_0x441229(0xb1)];_0x149e19||_0x3a10e1[_0x441229(0xfb)]<_0x26801b||_0x58880c?(this['serialize'](_0x5387e5,_0xc66542,_0x3a10e1,_0x58880c||{}),this['_additionalMetadata'](_0xc66542,_0x5387e5)):this[_0x441229(0x15e)](_0x5387e5,_0x3a10e1,_0xc66542,function(){var _0x4c5964=_0x441229;_0x444339==='null'||_0x444339===_0x4c5964(0x173)||(delete _0x5387e5[_0x4c5964(0x133)],_0x5387e5[_0x4c5964(0x12e)]=!0x0);});}return _0x5387e5;}finally{_0x3a10e1['expressionsToEvaluate']=_0x1027a2,_0x3a10e1[_0x441229(0xc4)]=_0x26801b,_0x3a10e1[_0x441229(0xe8)]=_0x4bc711;}}['_capIfString'](_0x23177a,_0x57744b,_0x9de741,_0x50c512){var _0x1f3a92=_0x332963,_0x18c970=_0x50c512[_0x1f3a92(0xde)]||_0x9de741[_0x1f3a92(0xde)];if((_0x23177a===_0x1f3a92(0xa7)||_0x23177a===_0x1f3a92(0x10f))&&_0x57744b[_0x1f3a92(0x133)]){let _0x5531f9=_0x57744b[_0x1f3a92(0x133)][_0x1f3a92(0x147)];_0x9de741[_0x1f3a92(0x163)]+=_0x5531f9,_0x9de741['allStrLength']>_0x9de741[_0x1f3a92(0x16a)]?(_0x57744b['capped']='',delete _0x57744b[_0x1f3a92(0x133)]):_0x5531f9>_0x18c970&&(_0x57744b[_0x1f3a92(0x12e)]=_0x57744b[_0x1f3a92(0x133)][_0x1f3a92(0xd6)](0x0,_0x18c970),delete _0x57744b['value']);}}[_0x332963(0x14c)](_0x111b98){var _0xbd3557=_0x332963;return!!(_0x111b98&&_0x49fb52[_0xbd3557(0x110)]&&this[_0xbd3557(0x10d)](_0x111b98)===_0xbd3557(0x13b)&&_0x111b98[_0xbd3557(0xe0)]);}[_0x332963(0xe4)](_0x13fbd9){var _0x6f8f9d=_0x332963;if(_0x13fbd9[_0x6f8f9d(0x109)](/^\\\\d+$/))return _0x13fbd9;var _0x3520d5;try{_0x3520d5=JSON[_0x6f8f9d(0x14d)](''+_0x13fbd9);}catch{_0x3520d5='\\\\x22'+this['_objectToString'](_0x13fbd9)+'\\\\x22';}return _0x3520d5[_0x6f8f9d(0x109)](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x3520d5=_0x3520d5['substr'](0x1,_0x3520d5['length']-0x2):_0x3520d5=_0x3520d5[_0x6f8f9d(0xd2)](/'/g,'\\\\x5c\\\\x27')['replace'](/\\\\\\\\\\\"/g,'\\\\x22')['replace'](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x3520d5;}[_0x332963(0x15e)](_0x28ee77,_0x59ca48,_0x5b8289,_0x2f4bdc){var _0x589edc=_0x332963;this[_0x589edc(0xcb)](_0x28ee77,_0x59ca48),_0x2f4bdc&&_0x2f4bdc(),this[_0x589edc(0xa3)](_0x5b8289,_0x28ee77),this['_treeNodePropertiesAfterFullValue'](_0x28ee77,_0x59ca48);}[_0x332963(0xcb)](_0x4b101f,_0x5ef121){var _0x56b839=_0x332963;this['_setNodeId'](_0x4b101f,_0x5ef121),this[_0x56b839(0xb9)](_0x4b101f,_0x5ef121),this[_0x56b839(0xd1)](_0x4b101f,_0x5ef121),this[_0x56b839(0x10a)](_0x4b101f,_0x5ef121);}['_setNodeId'](_0x48ca3e,_0x1ff288){}['_setNodeQueryPath'](_0x76961d,_0x13a7a9){}[_0x332963(0x13c)](_0x548dd3,_0x109cdd){}['_isUndefined'](_0x1b33ce){var _0x25e2dd=_0x332963;return _0x1b33ce===this[_0x25e2dd(0xa1)];}[_0x332963(0x119)](_0x471a11,_0x101d49){var _0xb5e181=_0x332963;this[_0xb5e181(0x13c)](_0x471a11,_0x101d49),this['_setNodeExpandableState'](_0x471a11),_0x101d49[_0xb5e181(0x91)]&&this[_0xb5e181(0x9d)](_0x471a11),this[_0xb5e181(0x9f)](_0x471a11,_0x101d49),this[_0xb5e181(0x111)](_0x471a11,_0x101d49),this['_cleanNode'](_0x471a11);}[_0x332963(0xa3)](_0x4f0420,_0x2bc46e){var _0x152184=_0x332963;try{_0x4f0420&&typeof _0x4f0420[_0x152184(0x147)]==_0x152184(0x14b)&&(_0x2bc46e[_0x152184(0x147)]=_0x4f0420[_0x152184(0x147)]);}catch{}if(_0x2bc46e[_0x152184(0x144)]===_0x152184(0x14b)||_0x2bc46e[_0x152184(0x144)]==='Number'){if(isNaN(_0x2bc46e['value']))_0x2bc46e[_0x152184(0x166)]=!0x0,delete _0x2bc46e[_0x152184(0x133)];else switch(_0x2bc46e['value']){case Number[_0x152184(0x87)]:_0x2bc46e[_0x152184(0x164)]=!0x0,delete _0x2bc46e[_0x152184(0x133)];break;case Number[_0x152184(0x93)]:_0x2bc46e[_0x152184(0xbd)]=!0x0,delete _0x2bc46e['value'];break;case 0x0:this[_0x152184(0xd8)](_0x2bc46e['value'])&&(_0x2bc46e[_0x152184(0x17a)]=!0x0);break;}}else _0x2bc46e[_0x152184(0x144)]==='function'&&typeof _0x4f0420[_0x152184(0x150)]==_0x152184(0xa7)&&_0x4f0420[_0x152184(0x150)]&&_0x2bc46e[_0x152184(0x150)]&&_0x4f0420['name']!==_0x2bc46e[_0x152184(0x150)]&&(_0x2bc46e[_0x152184(0x90)]=_0x4f0420[_0x152184(0x150)]);}[_0x332963(0xd8)](_0x3a9623){var _0x364520=_0x332963;return 0x1/_0x3a9623===Number[_0x364520(0x93)];}[_0x332963(0x9d)](_0x5a1aa0){var _0x3420c1=_0x332963;!_0x5a1aa0[_0x3420c1(0x8b)]||!_0x5a1aa0[_0x3420c1(0x8b)]['length']||_0x5a1aa0[_0x3420c1(0x144)]===_0x3420c1(0x114)||_0x5a1aa0['type']===_0x3420c1(0x110)||_0x5a1aa0[_0x3420c1(0x144)]===_0x3420c1(0x136)||_0x5a1aa0['props']['sort'](function(_0x5cc905,_0x10c721){var _0x26af24=_0x3420c1,_0x2c03cb=_0x5cc905[_0x26af24(0x150)][_0x26af24(0x172)](),_0x368eeb=_0x10c721[_0x26af24(0x150)][_0x26af24(0x172)]();return _0x2c03cb<_0x368eeb?-0x1:_0x2c03cb>_0x368eeb?0x1:0x0;});}[_0x332963(0x9f)](_0x2bcde0,_0x45f29e){var _0x36a1a6=_0x332963;if(!(_0x45f29e[_0x36a1a6(0xfa)]||!_0x2bcde0[_0x36a1a6(0x8b)]||!_0x2bcde0[_0x36a1a6(0x8b)][_0x36a1a6(0x147)])){for(var _0x50e891=[],_0x4fa8a4=[],_0x49606c=0x0,_0x4a8171=_0x2bcde0['props']['length'];_0x49606c<_0x4a8171;_0x49606c++){var _0x35969c=_0x2bcde0[_0x36a1a6(0x8b)][_0x49606c];_0x35969c[_0x36a1a6(0x144)]===_0x36a1a6(0x95)?_0x50e891['push'](_0x35969c):_0x4fa8a4[_0x36a1a6(0xa8)](_0x35969c);}if(!(!_0x4fa8a4[_0x36a1a6(0x147)]||_0x50e891[_0x36a1a6(0x147)]<=0x1)){_0x2bcde0[_0x36a1a6(0x8b)]=_0x4fa8a4;var _0x13e28b={'functionsNode':!0x0,'props':_0x50e891};this['_setNodeId'](_0x13e28b,_0x45f29e),this['_setNodeLabel'](_0x13e28b,_0x45f29e),this[_0x36a1a6(0x12b)](_0x13e28b),this[_0x36a1a6(0x10a)](_0x13e28b,_0x45f29e),_0x13e28b['id']+='\\\\x20f',_0x2bcde0['props'][_0x36a1a6(0xbb)](_0x13e28b);}}}['_addLoadNode'](_0x45fa23,_0x512419){}[_0x332963(0x12b)](_0x1348ef){}[_0x332963(0xb3)](_0x2d77e2){var _0x34ec5e=_0x332963;return Array[_0x34ec5e(0x104)](_0x2d77e2)||typeof _0x2d77e2==_0x34ec5e(0x145)&&this[_0x34ec5e(0x10d)](_0x2d77e2)===_0x34ec5e(0xe6);}[_0x332963(0x10a)](_0x13e2c5,_0x4e3d57){}[_0x332963(0xdd)](_0x6d59a3){var _0x45b3d2=_0x332963;delete _0x6d59a3[_0x45b3d2(0x120)],delete _0x6d59a3[_0x45b3d2(0x85)],delete _0x6d59a3[_0x45b3d2(0x167)];}[_0x332963(0xd1)](_0x3b98b3,_0x3ff047){}}let _0x4cae64=new _0x25ba71(),_0x3be478={'props':0x64,'elements':0x64,'strLength':0x400*0x32,'totalStrLength':0x400*0x32,'autoExpandLimit':0x1388,'autoExpandMaxDepth':0xa},_0x190694={'props':0x5,'elements':0x5,'strLength':0x100,'totalStrLength':0x100*0x3,'autoExpandLimit':0x1e,'autoExpandMaxDepth':0x2};function _0x362f67(_0x2b7d46,_0x3f2212,_0x24baae,_0x140847,_0x582655,_0x269821){var _0x2b765d=_0x332963;let _0x53603a,_0x2b906b;try{_0x2b906b=_0x4b914c(),_0x53603a=_0x260c36[_0x3f2212],!_0x53603a||_0x2b906b-_0x53603a['ts']>0x1f4&&_0x53603a[_0x2b765d(0x12c)]&&_0x53603a[_0x2b765d(0x159)]/_0x53603a[_0x2b765d(0x12c)]<0x64?(_0x260c36[_0x3f2212]=_0x53603a={'count':0x0,'time':0x0,'ts':_0x2b906b},_0x260c36[_0x2b765d(0x15a)]={}):_0x2b906b-_0x260c36[_0x2b765d(0x15a)]['ts']>0x32&&_0x260c36['hits']['count']&&_0x260c36['hits'][_0x2b765d(0x159)]/_0x260c36['hits']['count']<0x64&&(_0x260c36[_0x2b765d(0x15a)]={});let _0xd26fd1=[],_0x2a7870=_0x53603a[_0x2b765d(0x98)]||_0x260c36[_0x2b765d(0x15a)][_0x2b765d(0x98)]?_0x190694:_0x3be478,_0x2c78c9=_0x19333e=>{var _0x26096b=_0x2b765d;let _0x1d07b0={};return _0x1d07b0[_0x26096b(0x8b)]=_0x19333e['props'],_0x1d07b0[_0x26096b(0x8e)]=_0x19333e[_0x26096b(0x8e)],_0x1d07b0[_0x26096b(0xde)]=_0x19333e[_0x26096b(0xde)],_0x1d07b0[_0x26096b(0x16a)]=_0x19333e[_0x26096b(0x16a)],_0x1d07b0[_0x26096b(0xb1)]=_0x19333e[_0x26096b(0xb1)],_0x1d07b0['autoExpandMaxDepth']=_0x19333e['autoExpandMaxDepth'],_0x1d07b0[_0x26096b(0x91)]=!0x1,_0x1d07b0[_0x26096b(0xfa)]=!_0x7016a8,_0x1d07b0[_0x26096b(0xc4)]=0x1,_0x1d07b0[_0x26096b(0xfb)]=0x0,_0x1d07b0[_0x26096b(0x13a)]='root_exp_id',_0x1d07b0[_0x26096b(0x154)]=_0x26096b(0xf9),_0x1d07b0[_0x26096b(0xcf)]=!0x0,_0x1d07b0['autoExpandPreviousObjects']=[],_0x1d07b0['autoExpandPropertyCount']=0x0,_0x1d07b0[_0x26096b(0xe1)]=!0x0,_0x1d07b0[_0x26096b(0x163)]=0x0,_0x1d07b0['node']={'current':void 0x0,'parent':void 0x0,'index':0x0},_0x1d07b0;};for(var _0x31d88b=0x0;_0x31d88b<_0x582655[_0x2b765d(0x147)];_0x31d88b++)_0xd26fd1[_0x2b765d(0xa8)](_0x4cae64[_0x2b765d(0x132)]({'timeNode':_0x2b7d46==='time'||void 0x0},_0x582655[_0x31d88b],_0x2c78c9(_0x2a7870),{}));if(_0x2b7d46==='trace'||_0x2b7d46==='error'){let _0x5a02d3=Error[_0x2b765d(0xf8)];try{Error['stackTraceLimit']=0x1/0x0,_0xd26fd1['push'](_0x4cae64[_0x2b765d(0x132)]({'stackNode':!0x0},new Error()['stack'],_0x2c78c9(_0x2a7870),{'strLength':0x1/0x0}));}finally{Error[_0x2b765d(0xf8)]=_0x5a02d3;}}return{'method':_0x2b765d(0x160),'version':_0x176c7b,'args':[{'ts':_0x24baae,'session':_0x140847,'args':_0xd26fd1,'id':_0x3f2212,'context':_0x269821}]};}catch(_0x52251c){return{'method':'log','version':_0x176c7b,'args':[{'ts':_0x24baae,'session':_0x140847,'args':[{'type':_0x2b765d(0x12f),'error':_0x52251c&&_0x52251c[_0x2b765d(0x128)]}],'id':_0x3f2212,'context':_0x269821}]};}finally{try{if(_0x53603a&&_0x2b906b){let _0xbd3de3=_0x4b914c();_0x53603a[_0x2b765d(0x12c)]++,_0x53603a[_0x2b765d(0x159)]+=_0x1525d9(_0x2b906b,_0xbd3de3),_0x53603a['ts']=_0xbd3de3,_0x260c36['hits'][_0x2b765d(0x12c)]++,_0x260c36[_0x2b765d(0x15a)]['time']+=_0x1525d9(_0x2b906b,_0xbd3de3),_0x260c36[_0x2b765d(0x15a)]['ts']=_0xbd3de3,(_0x53603a[_0x2b765d(0x12c)]>0x32||_0x53603a['time']>0x64)&&(_0x53603a[_0x2b765d(0x98)]=!0x0),(_0x260c36[_0x2b765d(0x15a)][_0x2b765d(0x12c)]>0x3e8||_0x260c36[_0x2b765d(0x15a)][_0x2b765d(0x159)]>0x12c)&&(_0x260c36[_0x2b765d(0x15a)][_0x2b765d(0x98)]=!0x0);}}catch{}}}return _0x362f67;}function _0x4f1d(){var _0x205f76=['call','slice','expId','[object\\\\x20Map]','_setNodeLabel','12pdpWXs','port','eventReceivedCallback','symbol','split','RegExp','_blacklistedProperty','type','object','','length','_ws','\\\\x20server','some','number','_isMap','stringify','onclose','hasOwnProperty','name','readyState','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','console','rootExpression','_addObjectProperty','timeStamp','astro','elapsed','time','hits','disabledLog','173390ZscRgQ','_webSocketErrorDocsLink','_processTreeNodeResult','dockerizedApp','log','1752129174464','next.js','allStrLength','positiveInfinity','_isPrimitiveType','nan','_hasMapOnItsPath','1.0.0','edge','totalStrLength','_isSet','_reconnectTimeout','94820FiKDkv','process','bind','onopen','join','toLowerCase','undefined','[object\\\\x20BigInt]','https://tinyurl.com/37x8b79t','1','test','null','prototype','negativeZero','performance','_sendErrorMessage','[object\\\\x20Date]','fromCharCode','_hasSetOnItsPath','current','POSITIVE_INFINITY','next.js','constructor','getWebSocketClass','props','_socket','getPrototypeOf','elements','_type','funcName','sortProps','valueOf','NEGATIVE_INFINITY','207097oRiKvk','function','getter','path','reduceLimits','index','method','bigint','587709TZTXEI','_sortProps','ws://','_addFunctionsNode','getOwnPropertyDescriptor','_undefined','pathToFileURL','_additionalMetadata','_allowedToSend','then','catch','string','push','data','2895BPAbem','parent','_HTMLAllCollection','autoExpandPropertyCount','...','_property','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','autoExpandLimit','onmessage','_isArray','expressionsToEvaluate','_p_','_getOwnPropertySymbols','_inBrowser','WebSocket','_setNodeQueryPath','toString','unshift','versions','negativeInfinity','map','logger\\\\x20websocket\\\\x20error','host','\\\\x20browser','_quotedRegExp','includes','depth','Buffer','_p_length','_allowedToConnectOnSend','_connectToHostNow','20oTdlYD','default','_treeNodePropertiesBeforeFullValue','create','_regExpToString','_Symbol','autoExpand','autoExpandMaxDepth','_setNodeExpressionPath','replace','get','onerror','angular','substr','location','_isNegativeZero','concat','hostname','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','8pfeevL','_cleanNode','strLength','date','forEach','resolveGetters','origin','getOwnPropertyNames','_propertyName','set','[object\\\\x20Array]','_consoleNinjaAllowedToStart','isExpressionToEvaluate','global','startsWith','_addProperty','Boolean','_console_ninja','596316tCCDwD','_isPrimitiveWrapperType','_dateToString','_connected','remix','_disposeWebsocket','_console_ninja_session','_ninjaIgnoreNextError','hrtime','unref','stackTraceLimit','root_exp','noFunctions','level','127.0.0.1','reload','send','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','ws/index.js',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"DESKTOP-5O96LAU\\\",\\\"***************\\\"],'354565dupkoD','Symbol','isArray','_extendedWarning','node','Number','cappedElements','match','_setNodePermissions','close','NEXT_RUNTIME','_objectToString','_connecting','String','Map','_addLoadNode','warn','perf_hooks','array','gateway.docker.internal','_WebSocketClass','_keyStrRegExp','_maxConnectAttemptCount','_treeNodePropertiesAfterFullValue','env','autoExpandPreviousObjects','args','toUpperCase','trace','','_hasSymbolPropertyOnItsPath','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','error','now','_connectAttemptCount','nodeModules','getOwnPropertySymbols','_capIfString','message','_inNextEdge','parse','_setNodeExpandableState','count','_attemptToReconnectShortly','capped','unknown','coverage','_getOwnPropertyNames','serialize','value','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','_WebSocket','Set','enumerable'];_0x4f1d=function(){return _0x205f76;};return _0x4f1d();}((_0x4085a0,_0x534cf1,_0xe27ec5,_0x1b30c3,_0x182771,_0x1a863b,_0x4b5115,_0x5ce3b6,_0x4d1f29,_0x5f309d,_0x121a5e)=>{var _0x398d4b=_0x24f63b;if(_0x4085a0[_0x398d4b(0xed)])return _0x4085a0[_0x398d4b(0xed)];if(!X(_0x4085a0,_0x5ce3b6,_0x182771))return _0x4085a0[_0x398d4b(0xed)]={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}},_0x4085a0[_0x398d4b(0xed)];let _0x229219=B(_0x4085a0),_0x227bc2=_0x229219[_0x398d4b(0x158)],_0x4060dd=_0x229219[_0x398d4b(0x156)],_0x200f19=_0x229219[_0x398d4b(0x123)],_0x2d96b3={'hits':{},'ts':{}},_0x5eec24=J(_0x4085a0,_0x4d1f29,_0x2d96b3,_0x1a863b),_0xd4105e=_0x36c149=>{_0x2d96b3['ts'][_0x36c149]=_0x4060dd();},_0xc48e78=(_0x59b40b,_0x42217f)=>{var _0x1a20c6=_0x398d4b;let _0x27dedd=_0x2d96b3['ts'][_0x42217f];if(delete _0x2d96b3['ts'][_0x42217f],_0x27dedd){let _0x2db741=_0x227bc2(_0x27dedd,_0x4060dd());_0x3bb8e9(_0x5eec24(_0x1a20c6(0x159),_0x59b40b,_0x200f19(),_0x2682d1,[_0x2db741],_0x42217f));}},_0x66429c=_0x318690=>{var _0x5080eb=_0x398d4b,_0x12ea80;return _0x182771===_0x5080eb(0x88)&&_0x4085a0[_0x5080eb(0xe2)]&&((_0x12ea80=_0x318690==null?void 0x0:_0x318690[_0x5080eb(0x11c)])==null?void 0x0:_0x12ea80[_0x5080eb(0x147)])&&(_0x318690[_0x5080eb(0x11c)][0x0][_0x5080eb(0xe2)]=_0x4085a0[_0x5080eb(0xe2)]),_0x318690;};_0x4085a0[_0x398d4b(0xed)]={'consoleLog':(_0x41dfec,_0x49e1a2)=>{var _0x4cf132=_0x398d4b;_0x4085a0[_0x4cf132(0x153)][_0x4cf132(0x160)][_0x4cf132(0x150)]!==_0x4cf132(0x15b)&&_0x3bb8e9(_0x5eec24(_0x4cf132(0x160),_0x41dfec,_0x200f19(),_0x2682d1,_0x49e1a2));},'consoleTrace':(_0x1d9ea6,_0x240ac6)=>{var _0x5354a9=_0x398d4b,_0x107585,_0x20e659;_0x4085a0[_0x5354a9(0x153)][_0x5354a9(0x160)]['name']!=='disabledTrace'&&((_0x20e659=(_0x107585=_0x4085a0[_0x5354a9(0x16e)])==null?void 0x0:_0x107585[_0x5354a9(0xbc)])!=null&&_0x20e659[_0x5354a9(0x106)]&&(_0x4085a0['_ninjaIgnoreNextError']=!0x0),_0x3bb8e9(_0x66429c(_0x5eec24(_0x5354a9(0x11e),_0x1d9ea6,_0x200f19(),_0x2682d1,_0x240ac6))));},'consoleError':(_0x176065,_0x256e9c)=>{var _0x423e20=_0x398d4b;_0x4085a0[_0x423e20(0xf5)]=!0x0,_0x3bb8e9(_0x66429c(_0x5eec24(_0x423e20(0x122),_0x176065,_0x200f19(),_0x2682d1,_0x256e9c)));},'consoleTime':_0x4cfae9=>{_0xd4105e(_0x4cfae9);},'consoleTimeEnd':(_0x261222,_0x55f10f)=>{_0xc48e78(_0x55f10f,_0x261222);},'autoLog':(_0xdb7cb3,_0x2c6ea2)=>{var _0x17255f=_0x398d4b;_0x3bb8e9(_0x5eec24(_0x17255f(0x160),_0x2c6ea2,_0x200f19(),_0x2682d1,[_0xdb7cb3]));},'autoLogMany':(_0x19ddec,_0x1abcc0)=>{_0x3bb8e9(_0x5eec24('log',_0x19ddec,_0x200f19(),_0x2682d1,_0x1abcc0));},'autoTrace':(_0x46922a,_0x14d21e)=>{var _0x2e55ed=_0x398d4b;_0x3bb8e9(_0x66429c(_0x5eec24(_0x2e55ed(0x11e),_0x14d21e,_0x200f19(),_0x2682d1,[_0x46922a])));},'autoTraceMany':(_0x1c3f68,_0x1d3a71)=>{var _0x3d331a=_0x398d4b;_0x3bb8e9(_0x66429c(_0x5eec24(_0x3d331a(0x11e),_0x1c3f68,_0x200f19(),_0x2682d1,_0x1d3a71)));},'autoTime':(_0x2b841b,_0x1d7f8a,_0x38eb7b)=>{_0xd4105e(_0x38eb7b);},'autoTimeEnd':(_0x2bef07,_0x11a0ca,_0x3a3237)=>{_0xc48e78(_0x11a0ca,_0x3a3237);},'coverage':_0x4f4dae=>{var _0x4a82be=_0x398d4b;_0x3bb8e9({'method':_0x4a82be(0x130),'version':_0x1a863b,'args':[{'id':_0x4f4dae}]});}};let _0x3bb8e9=H(_0x4085a0,_0x534cf1,_0xe27ec5,_0x1b30c3,_0x182771,_0x5f309d,_0x121a5e),_0x2682d1=_0x4085a0[_0x398d4b(0xf4)];return _0x4085a0[_0x398d4b(0xed)];})(globalThis,_0x24f63b(0xfc),'57520',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.cursor\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.457-universal\\\\\\\\node_modules\\\",_0x24f63b(0x162),_0x24f63b(0x168),_0x24f63b(0x161),_0x24f63b(0x101),_0x24f63b(0x146),_0x24f63b(0x11f),_0x24f63b(0x176));\");\n    } catch (e) {}\n}\nfunction oo_oo(i) {\n    for(var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        v[_key - 1] = arguments[_key];\n    }\n    try {\n        oo_cm().consoleLog(i, v);\n    } catch (e) {}\n    return v;\n}\noo_oo; /* istanbul ignore next */ \nfunction oo_tr(i) {\n    for(var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        v[_key - 1] = arguments[_key];\n    }\n    try {\n        oo_cm().consoleTrace(i, v);\n    } catch (e) {}\n    return v;\n}\noo_tr; /* istanbul ignore next */ \nfunction oo_tx(i) {\n    for(var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        v[_key - 1] = arguments[_key];\n    }\n    try {\n        oo_cm().consoleError(i, v);\n    } catch (e) {}\n    return v;\n}\noo_tx; /* istanbul ignore next */ \nfunction oo_ts(v) {\n    try {\n        oo_cm().consoleTime(v);\n    } catch (e) {}\n    return v;\n}\noo_ts; /* istanbul ignore next */ \nfunction oo_te(v, i) {\n    try {\n        oo_cm().consoleTimeEnd(v, i);\n    } catch (e) {}\n    return v;\n}\noo_te; /*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/ \nvar _c;\n$RefreshReg$(_c, \"TicketDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pms/manage_tickets/[id]/page.tsx\n"));

/***/ })

});