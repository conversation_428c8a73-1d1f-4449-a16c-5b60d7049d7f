/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fs.realpath";
exports.ids = ["vendor-chunks/fs.realpath"];
exports.modules = {

/***/ "(ssr)/./node_modules/fs.realpath/index.js":
/*!*******************************************!*\
  !*** ./node_modules/fs.realpath/index.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = realpath\nrealpath.realpath = realpath\nrealpath.sync = realpathSync\nrealpath.realpathSync = realpathSync\nrealpath.monkeypatch = monkeypatch\nrealpath.unmonkeypatch = unmonkeypatch\n\nvar fs = __webpack_require__(/*! fs */ \"fs\")\nvar origRealpath = fs.realpath\nvar origRealpathSync = fs.realpathSync\n\nvar version = process.version\nvar ok = /^v[0-5]\\./.test(version)\nvar old = __webpack_require__(/*! ./old.js */ \"(ssr)/./node_modules/fs.realpath/old.js\")\n\nfunction newError (er) {\n  return er && er.syscall === 'realpath' && (\n    er.code === 'ELOOP' ||\n    er.code === 'ENOMEM' ||\n    er.code === 'ENAMETOOLONG'\n  )\n}\n\nfunction realpath (p, cache, cb) {\n  if (ok) {\n    return origRealpath(p, cache, cb)\n  }\n\n  if (typeof cache === 'function') {\n    cb = cache\n    cache = null\n  }\n  origRealpath(p, cache, function (er, result) {\n    if (newError(er)) {\n      old.realpath(p, cache, cb)\n    } else {\n      cb(er, result)\n    }\n  })\n}\n\nfunction realpathSync (p, cache) {\n  if (ok) {\n    return origRealpathSync(p, cache)\n  }\n\n  try {\n    return origRealpathSync(p, cache)\n  } catch (er) {\n    if (newError(er)) {\n      return old.realpathSync(p, cache)\n    } else {\n      throw er\n    }\n  }\n}\n\nfunction monkeypatch () {\n  fs.realpath = realpath\n  fs.realpathSync = realpathSync\n}\n\nfunction unmonkeypatch () {\n  fs.realpath = origRealpath\n  fs.realpathSync = origRealpathSync\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fs.realpath/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fs.realpath/old.js":
/*!*****************************************!*\
  !*** ./node_modules/fs.realpath/old.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nvar pathModule = __webpack_require__(/*! path */ \"path\");\nvar isWindows = process.platform === 'win32';\nvar fs = __webpack_require__(/*! fs */ \"fs\");\n\n// JavaScript implementation of realpath, ported from node pre-v6\n\nvar DEBUG = process.env.NODE_DEBUG && /fs/.test(process.env.NODE_DEBUG);\n\nfunction rethrow() {\n  // Only enable in debug mode. A backtrace uses ~1000 bytes of heap space and\n  // is fairly slow to generate.\n  var callback;\n  if (DEBUG) {\n    var backtrace = new Error;\n    callback = debugCallback;\n  } else\n    callback = missingCallback;\n\n  return callback;\n\n  function debugCallback(err) {\n    if (err) {\n      backtrace.message = err.message;\n      err = backtrace;\n      missingCallback(err);\n    }\n  }\n\n  function missingCallback(err) {\n    if (err) {\n      if (process.throwDeprecation)\n        throw err;  // Forgot a callback but don't know where? Use NODE_DEBUG=fs\n      else if (!process.noDeprecation) {\n        var msg = 'fs: missing callback ' + (err.stack || err.message);\n        if (process.traceDeprecation)\n          console.trace(msg);\n        else\n          console.error(msg);\n      }\n    }\n  }\n}\n\nfunction maybeCallback(cb) {\n  return typeof cb === 'function' ? cb : rethrow();\n}\n\nvar normalize = pathModule.normalize;\n\n// Regexp that finds the next partion of a (partial) path\n// result is [base_with_slash, base], e.g. ['somedir/', 'somedir']\nif (isWindows) {\n  var nextPartRe = /(.*?)(?:[\\/\\\\]+|$)/g;\n} else {\n  var nextPartRe = /(.*?)(?:[\\/]+|$)/g;\n}\n\n// Regex to find the device root, including trailing slash. E.g. 'c:\\\\'.\nif (isWindows) {\n  var splitRootRe = /^(?:[a-zA-Z]:|[\\\\\\/]{2}[^\\\\\\/]+[\\\\\\/][^\\\\\\/]+)?[\\\\\\/]*/;\n} else {\n  var splitRootRe = /^[\\/]*/;\n}\n\nexports.realpathSync = function realpathSync(p, cache) {\n  // make p is absolute\n  p = pathModule.resolve(p);\n\n  if (cache && Object.prototype.hasOwnProperty.call(cache, p)) {\n    return cache[p];\n  }\n\n  var original = p,\n      seenLinks = {},\n      knownHard = {};\n\n  // current character position in p\n  var pos;\n  // the partial path so far, including a trailing slash if any\n  var current;\n  // the partial path without a trailing slash (except when pointing at a root)\n  var base;\n  // the partial path scanned in the previous round, with slash\n  var previous;\n\n  start();\n\n  function start() {\n    // Skip over roots\n    var m = splitRootRe.exec(p);\n    pos = m[0].length;\n    current = m[0];\n    base = m[0];\n    previous = '';\n\n    // On windows, check that the root exists. On unix there is no need.\n    if (isWindows && !knownHard[base]) {\n      fs.lstatSync(base);\n      knownHard[base] = true;\n    }\n  }\n\n  // walk down the path, swapping out linked pathparts for their real\n  // values\n  // NB: p.length changes.\n  while (pos < p.length) {\n    // find the next part\n    nextPartRe.lastIndex = pos;\n    var result = nextPartRe.exec(p);\n    previous = current;\n    current += result[0];\n    base = previous + result[1];\n    pos = nextPartRe.lastIndex;\n\n    // continue if not a symlink\n    if (knownHard[base] || (cache && cache[base] === base)) {\n      continue;\n    }\n\n    var resolvedLink;\n    if (cache && Object.prototype.hasOwnProperty.call(cache, base)) {\n      // some known symbolic link.  no need to stat again.\n      resolvedLink = cache[base];\n    } else {\n      var stat = fs.lstatSync(base);\n      if (!stat.isSymbolicLink()) {\n        knownHard[base] = true;\n        if (cache) cache[base] = base;\n        continue;\n      }\n\n      // read the link if it wasn't read before\n      // dev/ino always return 0 on windows, so skip the check.\n      var linkTarget = null;\n      if (!isWindows) {\n        var id = stat.dev.toString(32) + ':' + stat.ino.toString(32);\n        if (seenLinks.hasOwnProperty(id)) {\n          linkTarget = seenLinks[id];\n        }\n      }\n      if (linkTarget === null) {\n        fs.statSync(base);\n        linkTarget = fs.readlinkSync(base);\n      }\n      resolvedLink = pathModule.resolve(previous, linkTarget);\n      // track this, if given a cache.\n      if (cache) cache[base] = resolvedLink;\n      if (!isWindows) seenLinks[id] = linkTarget;\n    }\n\n    // resolve the link, then start over\n    p = pathModule.resolve(resolvedLink, p.slice(pos));\n    start();\n  }\n\n  if (cache) cache[original] = p;\n\n  return p;\n};\n\n\nexports.realpath = function realpath(p, cache, cb) {\n  if (typeof cb !== 'function') {\n    cb = maybeCallback(cache);\n    cache = null;\n  }\n\n  // make p is absolute\n  p = pathModule.resolve(p);\n\n  if (cache && Object.prototype.hasOwnProperty.call(cache, p)) {\n    return process.nextTick(cb.bind(null, null, cache[p]));\n  }\n\n  var original = p,\n      seenLinks = {},\n      knownHard = {};\n\n  // current character position in p\n  var pos;\n  // the partial path so far, including a trailing slash if any\n  var current;\n  // the partial path without a trailing slash (except when pointing at a root)\n  var base;\n  // the partial path scanned in the previous round, with slash\n  var previous;\n\n  start();\n\n  function start() {\n    // Skip over roots\n    var m = splitRootRe.exec(p);\n    pos = m[0].length;\n    current = m[0];\n    base = m[0];\n    previous = '';\n\n    // On windows, check that the root exists. On unix there is no need.\n    if (isWindows && !knownHard[base]) {\n      fs.lstat(base, function(err) {\n        if (err) return cb(err);\n        knownHard[base] = true;\n        LOOP();\n      });\n    } else {\n      process.nextTick(LOOP);\n    }\n  }\n\n  // walk down the path, swapping out linked pathparts for their real\n  // values\n  function LOOP() {\n    // stop if scanned past end of path\n    if (pos >= p.length) {\n      if (cache) cache[original] = p;\n      return cb(null, p);\n    }\n\n    // find the next part\n    nextPartRe.lastIndex = pos;\n    var result = nextPartRe.exec(p);\n    previous = current;\n    current += result[0];\n    base = previous + result[1];\n    pos = nextPartRe.lastIndex;\n\n    // continue if not a symlink\n    if (knownHard[base] || (cache && cache[base] === base)) {\n      return process.nextTick(LOOP);\n    }\n\n    if (cache && Object.prototype.hasOwnProperty.call(cache, base)) {\n      // known symbolic link.  no need to stat again.\n      return gotResolvedLink(cache[base]);\n    }\n\n    return fs.lstat(base, gotStat);\n  }\n\n  function gotStat(err, stat) {\n    if (err) return cb(err);\n\n    // if not a symlink, skip to the next path part\n    if (!stat.isSymbolicLink()) {\n      knownHard[base] = true;\n      if (cache) cache[base] = base;\n      return process.nextTick(LOOP);\n    }\n\n    // stat & read the link if not read before\n    // call gotTarget as soon as the link target is known\n    // dev/ino always return 0 on windows, so skip the check.\n    if (!isWindows) {\n      var id = stat.dev.toString(32) + ':' + stat.ino.toString(32);\n      if (seenLinks.hasOwnProperty(id)) {\n        return gotTarget(null, seenLinks[id], base);\n      }\n    }\n    fs.stat(base, function(err) {\n      if (err) return cb(err);\n\n      fs.readlink(base, function(err, target) {\n        if (!isWindows) seenLinks[id] = target;\n        gotTarget(err, target);\n      });\n    });\n  }\n\n  function gotTarget(err, target, base) {\n    if (err) return cb(err);\n\n    var resolvedLink = pathModule.resolve(previous, target);\n    if (cache) cache[base] = resolvedLink;\n    gotResolvedLink(resolvedLink);\n  }\n\n  function gotResolvedLink(resolvedLink) {\n    // resolve the link, then start over\n    p = pathModule.resolve(resolvedLink, p.slice(pos));\n    start();\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fs.realpath/old.js\n");

/***/ })

};
;