"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pms/layout",{

/***/ "(app-pages-browser)/./app/pms/manage_tickets/TicketContext.tsx":
/*!**************************************************!*\
  !*** ./app/pms/manage_tickets/TicketContext.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TicketContext: function() { return /* binding */ TicketContext; },\n/* harmony export */   TicketProvider: function() { return /* binding */ TicketProvider; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* __next_internal_client_entry_do_not_use__ TicketContext,TicketProvider auto */ \nvar _s = $RefreshSig$();\n\n\nconst TicketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    tickets: [],\n    setTickets: ()=>{},\n    users: [],\n    setUsers: ()=>{},\n    currentUser: null,\n    setCurrentUser: ()=>{}\n});\nfunction TicketProvider(param) {\n    let { children, initialCurrentUser } = param;\n    _s();\n    const [tickets, setTickets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialCurrentUser || null);\n    // Ensure currentUser is always hydrated\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (!currentUser) {\n            // Try to fetch the current user from the backend\n            fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_2__.employee_routes.GETCURRENT_USER, {\n                credentials: \"include\"\n            }).then((res)=>res.ok ? res.json() : null).then((user)=>{\n                if (user) {\n                    setCurrentUser(user);\n                }\n            }).catch(()=>{});\n        }\n    }, [\n        currentUser\n    ]);\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            tickets,\n            setTickets,\n            users,\n            setUsers,\n            currentUser,\n            setCurrentUser\n        }), [\n        tickets,\n        users,\n        currentUser\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TicketContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\TicketContext.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(TicketProvider, \"FQ5hj9W45YcfZzWbTrHO0koHFEo=\");\n_c = TicketProvider;\nvar _c;\n$RefreshReg$(_c, \"TicketProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wbXMvbWFuYWdlX3RpY2tldHMvVGlja2V0Q29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFZ0U7QUFDZDtBQVczQyxNQUFNSyw4QkFBZ0JKLG9EQUFhQSxDQUFvQjtJQUM1REssU0FBUyxFQUFFO0lBQ1hDLFlBQVksS0FBTztJQUNuQkMsT0FBTyxFQUFFO0lBQ1RDLFVBQVUsS0FBTztJQUNqQkMsYUFBYTtJQUNiQyxnQkFBZ0IsS0FBTztBQUN6QixHQUFHO0FBRUksU0FBU0MsZUFBZSxLQUF5RjtRQUF6RixFQUFFQyxRQUFRLEVBQUVDLGtCQUFrQixFQUEyRCxHQUF6Rjs7SUFDN0IsTUFBTSxDQUFDUixTQUFTQyxXQUFXLEdBQUdMLCtDQUFRQSxDQUFRLEVBQUU7SUFDaEQsTUFBTSxDQUFDTSxPQUFPQyxTQUFTLEdBQUdQLCtDQUFRQSxDQUFRLEVBQUU7SUFDNUMsTUFBTSxDQUFDUSxhQUFhQyxlQUFlLEdBQUdULCtDQUFRQSxDQUFNWSxzQkFBc0I7SUFFMUUsd0NBQXdDO0lBQ3hDZCxzREFBZSxDQUFDO1FBQ2QsSUFBSSxDQUFDVSxhQUFhO1lBQ2hCLGlEQUFpRDtZQUNqRE0sTUFBTVosMkRBQWVBLENBQUNhLGVBQWUsRUFBRTtnQkFBRUMsYUFBYTtZQUFVLEdBQzdEQyxJQUFJLENBQUNDLENBQUFBLE1BQU9BLElBQUlDLEVBQUUsR0FBR0QsSUFBSUUsSUFBSSxLQUFLLE1BQ2xDSCxJQUFJLENBQUNJLENBQUFBO2dCQUNKLElBQUlBLE1BQU07b0JBQ1JaLGVBQWVZO2dCQUNqQjtZQUNGLEdBQ0NDLEtBQUssQ0FBQyxLQUFPO1FBQ2xCO0lBQ0YsR0FBRztRQUFDZDtLQUFZO0lBRWhCLE1BQU1lLGVBQWV0Qiw4Q0FBT0EsQ0FDMUIsSUFBTztZQUFFRztZQUFTQztZQUFZQztZQUFPQztZQUFVQztZQUFhQztRQUFlLElBQzNFO1FBQUNMO1FBQVNFO1FBQU9FO0tBQVk7SUFHL0IscUJBQ0UsOERBQUNMLGNBQWNxQixRQUFRO1FBQUNDLE9BQU9GO2tCQUM1Qlo7Ozs7OztBQUdQO0dBOUJnQkQ7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL3Btcy9tYW5hZ2VfdGlja2V0cy9UaWNrZXRDb250ZXh0LnRzeD8wYjNjIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VTdGF0ZSwgdXNlTWVtbyB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBlbXBsb3llZV9yb3V0ZXMgfSBmcm9tIFwiQC9saWIvcm91dGVQYXRoXCI7XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFRpY2tldENvbnRleHRUeXBlIHtcclxuICB0aWNrZXRzOiBhbnlbXTtcclxuICBzZXRUaWNrZXRzOiBSZWFjdC5EaXNwYXRjaDxSZWFjdC5TZXRTdGF0ZUFjdGlvbjxhbnlbXT4+O1xyXG4gIHVzZXJzOiBhbnlbXTtcclxuICBzZXRVc2VyczogUmVhY3QuRGlzcGF0Y2g8UmVhY3QuU2V0U3RhdGVBY3Rpb248YW55W10+PjtcclxuICBjdXJyZW50VXNlcjogYW55O1xyXG4gIHNldEN1cnJlbnRVc2VyOiBSZWFjdC5EaXNwYXRjaDxSZWFjdC5TZXRTdGF0ZUFjdGlvbjxhbnk+PjtcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IFRpY2tldENvbnRleHQgPSBjcmVhdGVDb250ZXh0PFRpY2tldENvbnRleHRUeXBlPih7XHJcbiAgdGlja2V0czogW10sXHJcbiAgc2V0VGlja2V0czogKCkgPT4ge30sXHJcbiAgdXNlcnM6IFtdLFxyXG4gIHNldFVzZXJzOiAoKSA9PiB7fSxcclxuICBjdXJyZW50VXNlcjogbnVsbCxcclxuICBzZXRDdXJyZW50VXNlcjogKCkgPT4ge30sXHJcbn0pO1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIFRpY2tldFByb3ZpZGVyKHsgY2hpbGRyZW4sIGluaXRpYWxDdXJyZW50VXNlciB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUsIGluaXRpYWxDdXJyZW50VXNlcj86IGFueSB9KSB7XHJcbiAgY29uc3QgW3RpY2tldHMsIHNldFRpY2tldHNdID0gdXNlU3RhdGU8YW55W10+KFtdKTtcclxuICBjb25zdCBbdXNlcnMsIHNldFVzZXJzXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XHJcbiAgY29uc3QgW2N1cnJlbnRVc2VyLCBzZXRDdXJyZW50VXNlcl0gPSB1c2VTdGF0ZTxhbnk+KGluaXRpYWxDdXJyZW50VXNlciB8fCBudWxsKTtcclxuXHJcbiAgLy8gRW5zdXJlIGN1cnJlbnRVc2VyIGlzIGFsd2F5cyBoeWRyYXRlZFxyXG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoIWN1cnJlbnRVc2VyKSB7XHJcbiAgICAgIC8vIFRyeSB0byBmZXRjaCB0aGUgY3VycmVudCB1c2VyIGZyb20gdGhlIGJhY2tlbmRcclxuICAgICAgZmV0Y2goZW1wbG95ZWVfcm91dGVzLkdFVENVUlJFTlRfVVNFUiwgeyBjcmVkZW50aWFsczogXCJpbmNsdWRlXCIgfSlcclxuICAgICAgICAudGhlbihyZXMgPT4gcmVzLm9rID8gcmVzLmpzb24oKSA6IG51bGwpXHJcbiAgICAgICAgLnRoZW4odXNlciA9PiB7XHJcbiAgICAgICAgICBpZiAodXNlcikge1xyXG4gICAgICAgICAgICBzZXRDdXJyZW50VXNlcih1c2VyKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KVxyXG4gICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7XHJcbiAgICB9XHJcbiAgfSwgW2N1cnJlbnRVc2VyXSk7XHJcblxyXG4gIGNvbnN0IGNvbnRleHRWYWx1ZSA9IHVzZU1lbW8oXHJcbiAgICAoKSA9PiAoeyB0aWNrZXRzLCBzZXRUaWNrZXRzLCB1c2Vycywgc2V0VXNlcnMsIGN1cnJlbnRVc2VyLCBzZXRDdXJyZW50VXNlciB9KSxcclxuICAgIFt0aWNrZXRzLCB1c2VycywgY3VycmVudFVzZXJdXHJcbiAgKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxUaWNrZXRDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXtjb250ZXh0VmFsdWV9PlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICA8L1RpY2tldENvbnRleHQuUHJvdmlkZXI+XHJcbiAgKTtcclxufSAiXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlU3RhdGUiLCJ1c2VNZW1vIiwiZW1wbG95ZWVfcm91dGVzIiwiVGlja2V0Q29udGV4dCIsInRpY2tldHMiLCJzZXRUaWNrZXRzIiwidXNlcnMiLCJzZXRVc2VycyIsImN1cnJlbnRVc2VyIiwic2V0Q3VycmVudFVzZXIiLCJUaWNrZXRQcm92aWRlciIsImNoaWxkcmVuIiwiaW5pdGlhbEN1cnJlbnRVc2VyIiwidXNlRWZmZWN0IiwiZmV0Y2giLCJHRVRDVVJSRU5UX1VTRVIiLCJjcmVkZW50aWFscyIsInRoZW4iLCJyZXMiLCJvayIsImpzb24iLCJ1c2VyIiwiY2F0Y2giLCJjb250ZXh0VmFsdWUiLCJQcm92aWRlciIsInZhbHVlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pms/manage_tickets/TicketContext.tsx\n"));

/***/ })

});