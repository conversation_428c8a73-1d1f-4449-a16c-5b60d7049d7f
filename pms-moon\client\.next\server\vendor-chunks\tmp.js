/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tmp";
exports.ids = ["vendor-chunks/tmp"];
exports.modules = {

/***/ "(ssr)/./node_modules/tmp/lib/tmp.js":
/*!*************************************!*\
  !*** ./node_modules/tmp/lib/tmp.js ***!
  \*************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * Tmp\n *\n * Copyright (c) 2011-2017 KARASZI Istvan <<EMAIL>>\n *\n * MIT Licensed\n */\n\n/*\n * Module dependencies.\n */\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst os = __webpack_require__(/*! os */ \"os\");\nconst path = __webpack_require__(/*! path */ \"path\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst _c = { fs: fs.constants, os: os.constants };\n\n/*\n * The working inner variables.\n */\nconst\n  // the random characters to choose from\n  RANDOM_CHARS = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',\n\n  TEMPLATE_PATTERN = /XXXXXX/,\n\n  DEFAULT_TRIES = 3,\n\n  CREATE_FLAGS = (_c.O_CREAT || _c.fs.O_CREAT) | (_c.O_EXCL || _c.fs.O_EXCL) | (_c.O_RDWR || _c.fs.O_RDWR),\n\n  // constants are off on the windows platform and will not match the actual errno codes\n  IS_WIN32 = os.platform() === 'win32',\n  EBADF = _c.EBADF || _c.os.errno.EBADF,\n  ENOENT = _c.ENOENT || _c.os.errno.ENOENT,\n\n  DIR_MODE = 0o700 /* 448 */,\n  FILE_MODE = 0o600 /* 384 */,\n\n  EXIT = 'exit',\n\n  // this will hold the objects need to be removed on exit\n  _removeObjects = [],\n\n  // API change in fs.rmdirSync leads to error when passing in a second parameter, e.g. the callback\n  FN_RMDIR_SYNC = fs.rmdirSync.bind(fs);\n\nlet\n  _gracefulCleanup = false;\n\n/**\n * Recursively remove a directory and its contents.\n *\n * @param {string} dirPath path of directory to remove\n * @param {Function} callback\n * @private\n */\nfunction rimraf(dirPath, callback) {\n  return fs.rm(dirPath, { recursive: true }, callback);\n}\n\n/**\n * Recursively remove a directory and its contents, synchronously.\n *\n * @param {string} dirPath path of directory to remove\n * @private\n */\nfunction FN_RIMRAF_SYNC(dirPath) {\n  return fs.rmSync(dirPath, { recursive: true });\n}\n\n/**\n * Gets a temporary file name.\n *\n * @param {(Options|tmpNameCallback)} options options or callback\n * @param {?tmpNameCallback} callback the callback function\n */\nfunction tmpName(options, callback) {\n  const\n    args = _parseArguments(options, callback),\n    opts = args[0],\n    cb = args[1];\n\n  try {\n    _assertAndSanitizeOptions(opts);\n  } catch (err) {\n    return cb(err);\n  }\n\n  let tries = opts.tries;\n  (function _getUniqueName() {\n    try {\n      const name = _generateTmpName(opts);\n\n      // check whether the path exists then retry if needed\n      fs.stat(name, function (err) {\n        /* istanbul ignore else */\n        if (!err) {\n          /* istanbul ignore else */\n          if (tries-- > 0) return _getUniqueName();\n\n          return cb(new Error('Could not get a unique tmp filename, max tries reached ' + name));\n        }\n\n        cb(null, name);\n      });\n    } catch (err) {\n      cb(err);\n    }\n  }());\n}\n\n/**\n * Synchronous version of tmpName.\n *\n * @param {Object} options\n * @returns {string} the generated random name\n * @throws {Error} if the options are invalid or could not generate a filename\n */\nfunction tmpNameSync(options) {\n  const\n    args = _parseArguments(options),\n    opts = args[0];\n\n  _assertAndSanitizeOptions(opts);\n\n  let tries = opts.tries;\n  do {\n    const name = _generateTmpName(opts);\n    try {\n      fs.statSync(name);\n    } catch (e) {\n      return name;\n    }\n  } while (tries-- > 0);\n\n  throw new Error('Could not get a unique tmp filename, max tries reached');\n}\n\n/**\n * Creates and opens a temporary file.\n *\n * @param {(Options|null|undefined|fileCallback)} options the config options or the callback function or null or undefined\n * @param {?fileCallback} callback\n */\nfunction file(options, callback) {\n  const\n    args = _parseArguments(options, callback),\n    opts = args[0],\n    cb = args[1];\n\n  // gets a temporary filename\n  tmpName(opts, function _tmpNameCreated(err, name) {\n    /* istanbul ignore else */\n    if (err) return cb(err);\n\n    // create and open the file\n    fs.open(name, CREATE_FLAGS, opts.mode || FILE_MODE, function _fileCreated(err, fd) {\n      /* istanbu ignore else */\n      if (err) return cb(err);\n\n      if (opts.discardDescriptor) {\n        return fs.close(fd, function _discardCallback(possibleErr) {\n          // the chance of getting an error on close here is rather low and might occur in the most edgiest cases only\n          return cb(possibleErr, name, undefined, _prepareTmpFileRemoveCallback(name, -1, opts, false));\n        });\n      } else {\n        // detachDescriptor passes the descriptor whereas discardDescriptor closes it, either way, we no longer care\n        // about the descriptor\n        const discardOrDetachDescriptor = opts.discardDescriptor || opts.detachDescriptor;\n        cb(null, name, fd, _prepareTmpFileRemoveCallback(name, discardOrDetachDescriptor ? -1 : fd, opts, false));\n      }\n    });\n  });\n}\n\n/**\n * Synchronous version of file.\n *\n * @param {Options} options\n * @returns {FileSyncObject} object consists of name, fd and removeCallback\n * @throws {Error} if cannot create a file\n */\nfunction fileSync(options) {\n  const\n    args = _parseArguments(options),\n    opts = args[0];\n\n  const discardOrDetachDescriptor = opts.discardDescriptor || opts.detachDescriptor;\n  const name = tmpNameSync(opts);\n  var fd = fs.openSync(name, CREATE_FLAGS, opts.mode || FILE_MODE);\n  /* istanbul ignore else */\n  if (opts.discardDescriptor) {\n    fs.closeSync(fd);\n    fd = undefined;\n  }\n\n  return {\n    name: name,\n    fd: fd,\n    removeCallback: _prepareTmpFileRemoveCallback(name, discardOrDetachDescriptor ? -1 : fd, opts, true)\n  };\n}\n\n/**\n * Creates a temporary directory.\n *\n * @param {(Options|dirCallback)} options the options or the callback function\n * @param {?dirCallback} callback\n */\nfunction dir(options, callback) {\n  const\n    args = _parseArguments(options, callback),\n    opts = args[0],\n    cb = args[1];\n\n  // gets a temporary filename\n  tmpName(opts, function _tmpNameCreated(err, name) {\n    /* istanbul ignore else */\n    if (err) return cb(err);\n\n    // create the directory\n    fs.mkdir(name, opts.mode || DIR_MODE, function _dirCreated(err) {\n      /* istanbul ignore else */\n      if (err) return cb(err);\n\n      cb(null, name, _prepareTmpDirRemoveCallback(name, opts, false));\n    });\n  });\n}\n\n/**\n * Synchronous version of dir.\n *\n * @param {Options} options\n * @returns {DirSyncObject} object consists of name and removeCallback\n * @throws {Error} if it cannot create a directory\n */\nfunction dirSync(options) {\n  const\n    args = _parseArguments(options),\n    opts = args[0];\n\n  const name = tmpNameSync(opts);\n  fs.mkdirSync(name, opts.mode || DIR_MODE);\n\n  return {\n    name: name,\n    removeCallback: _prepareTmpDirRemoveCallback(name, opts, true)\n  };\n}\n\n/**\n * Removes files asynchronously.\n *\n * @param {Object} fdPath\n * @param {Function} next\n * @private\n */\nfunction _removeFileAsync(fdPath, next) {\n  const _handler = function (err) {\n    if (err && !_isENOENT(err)) {\n      // reraise any unanticipated error\n      return next(err);\n    }\n    next();\n  };\n\n  if (0 <= fdPath[0])\n    fs.close(fdPath[0], function () {\n      fs.unlink(fdPath[1], _handler);\n    });\n  else fs.unlink(fdPath[1], _handler);\n}\n\n/**\n * Removes files synchronously.\n *\n * @param {Object} fdPath\n * @private\n */\nfunction _removeFileSync(fdPath) {\n  let rethrownException = null;\n  try {\n    if (0 <= fdPath[0]) fs.closeSync(fdPath[0]);\n  } catch (e) {\n    // reraise any unanticipated error\n    if (!_isEBADF(e) && !_isENOENT(e)) throw e;\n  } finally {\n    try {\n      fs.unlinkSync(fdPath[1]);\n    }\n    catch (e) {\n      // reraise any unanticipated error\n      if (!_isENOENT(e)) rethrownException = e;\n    }\n  }\n  if (rethrownException !== null) {\n    throw rethrownException;\n  }\n}\n\n/**\n * Prepares the callback for removal of the temporary file.\n *\n * Returns either a sync callback or a async callback depending on whether\n * fileSync or file was called, which is expressed by the sync parameter.\n *\n * @param {string} name the path of the file\n * @param {number} fd file descriptor\n * @param {Object} opts\n * @param {boolean} sync\n * @returns {fileCallback | fileCallbackSync}\n * @private\n */\nfunction _prepareTmpFileRemoveCallback(name, fd, opts, sync) {\n  const removeCallbackSync = _prepareRemoveCallback(_removeFileSync, [fd, name], sync);\n  const removeCallback = _prepareRemoveCallback(_removeFileAsync, [fd, name], sync, removeCallbackSync);\n\n  if (!opts.keep) _removeObjects.unshift(removeCallbackSync);\n\n  return sync ? removeCallbackSync : removeCallback;\n}\n\n/**\n * Prepares the callback for removal of the temporary directory.\n *\n * Returns either a sync callback or a async callback depending on whether\n * tmpFileSync or tmpFile was called, which is expressed by the sync parameter.\n *\n * @param {string} name\n * @param {Object} opts\n * @param {boolean} sync\n * @returns {Function} the callback\n * @private\n */\nfunction _prepareTmpDirRemoveCallback(name, opts, sync) {\n  const removeFunction = opts.unsafeCleanup ? rimraf : fs.rmdir.bind(fs);\n  const removeFunctionSync = opts.unsafeCleanup ? FN_RIMRAF_SYNC : FN_RMDIR_SYNC;\n  const removeCallbackSync = _prepareRemoveCallback(removeFunctionSync, name, sync);\n  const removeCallback = _prepareRemoveCallback(removeFunction, name, sync, removeCallbackSync);\n  if (!opts.keep) _removeObjects.unshift(removeCallbackSync);\n\n  return sync ? removeCallbackSync : removeCallback;\n}\n\n/**\n * Creates a guarded function wrapping the removeFunction call.\n *\n * The cleanup callback is save to be called multiple times.\n * Subsequent invocations will be ignored.\n *\n * @param {Function} removeFunction\n * @param {string} fileOrDirName\n * @param {boolean} sync\n * @param {cleanupCallbackSync?} cleanupCallbackSync\n * @returns {cleanupCallback | cleanupCallbackSync}\n * @private\n */\nfunction _prepareRemoveCallback(removeFunction, fileOrDirName, sync, cleanupCallbackSync) {\n  let called = false;\n\n  // if sync is true, the next parameter will be ignored\n  return function _cleanupCallback(next) {\n\n    /* istanbul ignore else */\n    if (!called) {\n      // remove cleanupCallback from cache\n      const toRemove = cleanupCallbackSync || _cleanupCallback;\n      const index = _removeObjects.indexOf(toRemove);\n      /* istanbul ignore else */\n      if (index >= 0) _removeObjects.splice(index, 1);\n\n      called = true;\n      if (sync || removeFunction === FN_RMDIR_SYNC || removeFunction === FN_RIMRAF_SYNC) {\n        return removeFunction(fileOrDirName);\n      } else {\n        return removeFunction(fileOrDirName, next || function() {});\n      }\n    }\n  };\n}\n\n/**\n * The garbage collector.\n *\n * @private\n */\nfunction _garbageCollector() {\n  /* istanbul ignore else */\n  if (!_gracefulCleanup) return;\n\n  // the function being called removes itself from _removeObjects,\n  // loop until _removeObjects is empty\n  while (_removeObjects.length) {\n    try {\n      _removeObjects[0]();\n    } catch (e) {\n      // already removed?\n    }\n  }\n}\n\n/**\n * Random name generator based on crypto.\n * Adapted from http://blog.tompawlak.org/how-to-generate-random-values-nodejs-javascript\n *\n * @param {number} howMany\n * @returns {string} the generated random name\n * @private\n */\nfunction _randomChars(howMany) {\n  let\n    value = [],\n    rnd = null;\n\n  // make sure that we do not fail because we ran out of entropy\n  try {\n    rnd = crypto.randomBytes(howMany);\n  } catch (e) {\n    rnd = crypto.pseudoRandomBytes(howMany);\n  }\n\n  for (var i = 0; i < howMany; i++) {\n    value.push(RANDOM_CHARS[rnd[i] % RANDOM_CHARS.length]);\n  }\n\n  return value.join('');\n}\n\n/**\n * Helper which determines whether a string s is blank, that is undefined, or empty or null.\n *\n * @private\n * @param {string} s\n * @returns {Boolean} true whether the string s is blank, false otherwise\n */\nfunction _isBlank(s) {\n  return s === null || _isUndefined(s) || !s.trim();\n}\n\n/**\n * Checks whether the `obj` parameter is defined or not.\n *\n * @param {Object} obj\n * @returns {boolean} true if the object is undefined\n * @private\n */\nfunction _isUndefined(obj) {\n  return typeof obj === 'undefined';\n}\n\n/**\n * Parses the function arguments.\n *\n * This function helps to have optional arguments.\n *\n * @param {(Options|null|undefined|Function)} options\n * @param {?Function} callback\n * @returns {Array} parsed arguments\n * @private\n */\nfunction _parseArguments(options, callback) {\n  /* istanbul ignore else */\n  if (typeof options === 'function') {\n    return [{}, options];\n  }\n\n  /* istanbul ignore else */\n  if (_isUndefined(options)) {\n    return [{}, callback];\n  }\n\n  // copy options so we do not leak the changes we make internally\n  const actualOptions = {};\n  for (const key of Object.getOwnPropertyNames(options)) {\n    actualOptions[key] = options[key];\n  }\n\n  return [actualOptions, callback];\n}\n\n/**\n * Generates a new temporary name.\n *\n * @param {Object} opts\n * @returns {string} the new random name according to opts\n * @private\n */\nfunction _generateTmpName(opts) {\n\n  const tmpDir = opts.tmpdir;\n\n  /* istanbul ignore else */\n  if (!_isUndefined(opts.name))\n    return path.join(tmpDir, opts.dir, opts.name);\n\n  /* istanbul ignore else */\n  if (!_isUndefined(opts.template))\n    return path.join(tmpDir, opts.dir, opts.template).replace(TEMPLATE_PATTERN, _randomChars(6));\n\n  // prefix and postfix\n  const name = [\n    opts.prefix ? opts.prefix : 'tmp',\n    '-',\n    process.pid,\n    '-',\n    _randomChars(12),\n    opts.postfix ? '-' + opts.postfix : ''\n  ].join('');\n\n  return path.join(tmpDir, opts.dir, name);\n}\n\n/**\n * Asserts whether the specified options are valid, also sanitizes options and provides sane defaults for missing\n * options.\n *\n * @param {Options} options\n * @private\n */\nfunction _assertAndSanitizeOptions(options) {\n\n  options.tmpdir = _getTmpDir(options);\n\n  const tmpDir = options.tmpdir;\n\n  /* istanbul ignore else */\n  if (!_isUndefined(options.name))\n    _assertIsRelative(options.name, 'name', tmpDir);\n  /* istanbul ignore else */\n  if (!_isUndefined(options.dir))\n    _assertIsRelative(options.dir, 'dir', tmpDir);\n  /* istanbul ignore else */\n  if (!_isUndefined(options.template)) {\n    _assertIsRelative(options.template, 'template', tmpDir);\n    if (!options.template.match(TEMPLATE_PATTERN))\n      throw new Error(`Invalid template, found \"${options.template}\".`);\n  }\n  /* istanbul ignore else */\n  if (!_isUndefined(options.tries) && isNaN(options.tries) || options.tries < 0)\n    throw new Error(`Invalid tries, found \"${options.tries}\".`);\n\n  // if a name was specified we will try once\n  options.tries = _isUndefined(options.name) ? options.tries || DEFAULT_TRIES : 1;\n  options.keep = !!options.keep;\n  options.detachDescriptor = !!options.detachDescriptor;\n  options.discardDescriptor = !!options.discardDescriptor;\n  options.unsafeCleanup = !!options.unsafeCleanup;\n\n  // sanitize dir, also keep (multiple) blanks if the user, purportedly sane, requests us to\n  options.dir = _isUndefined(options.dir) ? '' : path.relative(tmpDir, _resolvePath(options.dir, tmpDir));\n  options.template = _isUndefined(options.template) ? undefined : path.relative(tmpDir, _resolvePath(options.template, tmpDir));\n  // sanitize further if template is relative to options.dir\n  options.template = _isBlank(options.template) ? undefined : path.relative(options.dir, options.template);\n\n  // for completeness' sake only, also keep (multiple) blanks if the user, purportedly sane, requests us to\n  options.name = _isUndefined(options.name) ? undefined : options.name;\n  options.prefix = _isUndefined(options.prefix) ? '' : options.prefix;\n  options.postfix = _isUndefined(options.postfix) ? '' : options.postfix;\n}\n\n/**\n * Resolve the specified path name in respect to tmpDir.\n *\n * The specified name might include relative path components, e.g. ../\n * so we need to resolve in order to be sure that is is located inside tmpDir\n *\n * @param name\n * @param tmpDir\n * @returns {string}\n * @private\n */\nfunction _resolvePath(name, tmpDir) {\n  if (name.startsWith(tmpDir)) {\n    return path.resolve(name);\n  } else {\n    return path.resolve(path.join(tmpDir, name));\n  }\n}\n\n/**\n * Asserts whether specified name is relative to the specified tmpDir.\n *\n * @param {string} name\n * @param {string} option\n * @param {string} tmpDir\n * @throws {Error}\n * @private\n */\nfunction _assertIsRelative(name, option, tmpDir) {\n  if (option === 'name') {\n    // assert that name is not absolute and does not contain a path\n    if (path.isAbsolute(name))\n      throw new Error(`${option} option must not contain an absolute path, found \"${name}\".`);\n    // must not fail on valid .<name> or ..<name> or similar such constructs\n    let basename = path.basename(name);\n    if (basename === '..' || basename === '.' || basename !== name)\n      throw new Error(`${option} option must not contain a path, found \"${name}\".`);\n  }\n  else { // if (option === 'dir' || option === 'template') {\n    // assert that dir or template are relative to tmpDir\n    if (path.isAbsolute(name) && !name.startsWith(tmpDir)) {\n      throw new Error(`${option} option must be relative to \"${tmpDir}\", found \"${name}\".`);\n    }\n    let resolvedPath = _resolvePath(name, tmpDir);\n    if (!resolvedPath.startsWith(tmpDir))\n      throw new Error(`${option} option must be relative to \"${tmpDir}\", found \"${resolvedPath}\".`);\n  }\n}\n\n/**\n * Helper for testing against EBADF to compensate changes made to Node 7.x under Windows.\n *\n * @private\n */\nfunction _isEBADF(error) {\n  return _isExpectedError(error, -EBADF, 'EBADF');\n}\n\n/**\n * Helper for testing against ENOENT to compensate changes made to Node 7.x under Windows.\n *\n * @private\n */\nfunction _isENOENT(error) {\n  return _isExpectedError(error, -ENOENT, 'ENOENT');\n}\n\n/**\n * Helper to determine whether the expected error code matches the actual code and errno,\n * which will differ between the supported node versions.\n *\n * - Node >= 7.0:\n *   error.code {string}\n *   error.errno {number} any numerical value will be negated\n *\n * CAVEAT\n *\n * On windows, the errno for EBADF is -4083 but os.constants.errno.EBADF is different and we must assume that ENOENT\n * is no different here.\n *\n * @param {SystemError} error\n * @param {number} errno\n * @param {string} code\n * @private\n */\nfunction _isExpectedError(error, errno, code) {\n  return IS_WIN32 ? error.code === code : error.code === code && error.errno === errno;\n}\n\n/**\n * Sets the graceful cleanup.\n *\n * If graceful cleanup is set, tmp will remove all controlled temporary objects on process exit, otherwise the\n * temporary objects will remain in place, waiting to be cleaned up on system restart or otherwise scheduled temporary\n * object removals.\n */\nfunction setGracefulCleanup() {\n  _gracefulCleanup = true;\n}\n\n/**\n * Returns the currently configured tmp dir from os.tmpdir().\n *\n * @private\n * @param {?Options} options\n * @returns {string} the currently configured tmp dir\n */\nfunction _getTmpDir(options) {\n  return path.resolve(options && options.tmpdir || os.tmpdir());\n}\n\n// Install process exit listener\nprocess.addListener(EXIT, _garbageCollector);\n\n/**\n * Configuration options.\n *\n * @typedef {Object} Options\n * @property {?boolean} keep the temporary object (file or dir) will not be garbage collected\n * @property {?number} tries the number of tries before give up the name generation\n * @property (?int) mode the access mode, defaults are 0o700 for directories and 0o600 for files\n * @property {?string} template the \"mkstemp\" like filename template\n * @property {?string} name fixed name relative to tmpdir or the specified dir option\n * @property {?string} dir tmp directory relative to the root tmp directory in use\n * @property {?string} prefix prefix for the generated name\n * @property {?string} postfix postfix for the generated name\n * @property {?string} tmpdir the root tmp directory which overrides the os tmpdir\n * @property {?boolean} unsafeCleanup recursively removes the created temporary directory, even when it's not empty\n * @property {?boolean} detachDescriptor detaches the file descriptor, caller is responsible for closing the file, tmp will no longer try closing the file during garbage collection\n * @property {?boolean} discardDescriptor discards the file descriptor (closes file, fd is -1), tmp will no longer try closing the file during garbage collection\n */\n\n/**\n * @typedef {Object} FileSyncObject\n * @property {string} name the name of the file\n * @property {string} fd the file descriptor or -1 if the fd has been discarded\n * @property {fileCallback} removeCallback the callback function to remove the file\n */\n\n/**\n * @typedef {Object} DirSyncObject\n * @property {string} name the name of the directory\n * @property {fileCallback} removeCallback the callback function to remove the directory\n */\n\n/**\n * @callback tmpNameCallback\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n */\n\n/**\n * @callback fileCallback\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {number} fd the file descriptor or -1 if the fd had been discarded\n * @param {cleanupCallback} fn the cleanup callback function\n */\n\n/**\n * @callback fileCallbackSync\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {number} fd the file descriptor or -1 if the fd had been discarded\n * @param {cleanupCallbackSync} fn the cleanup callback function\n */\n\n/**\n * @callback dirCallback\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {cleanupCallback} fn the cleanup callback function\n */\n\n/**\n * @callback dirCallbackSync\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {cleanupCallbackSync} fn the cleanup callback function\n */\n\n/**\n * Removes the temporary created file or directory.\n *\n * @callback cleanupCallback\n * @param {simpleCallback} [next] function to call whenever the tmp object needs to be removed\n */\n\n/**\n * Removes the temporary created file or directory.\n *\n * @callback cleanupCallbackSync\n */\n\n/**\n * Callback function for function composition.\n * @see {@link https://github.com/raszi/node-tmp/issues/57|raszi/node-tmp#57}\n *\n * @callback simpleCallback\n */\n\n// exporting all the needed methods\n\n// evaluate _getTmpDir() lazily, mainly for simplifying testing but it also will\n// allow users to reconfigure the temporary directory\nObject.defineProperty(module.exports, \"tmpdir\", ({\n  enumerable: true,\n  configurable: false,\n  get: function () {\n    return _getTmpDir();\n  }\n}));\n\nmodule.exports.dir = dir;\nmodule.exports.dirSync = dirSync;\n\nmodule.exports.file = file;\nmodule.exports.fileSync = fileSync;\n\nmodule.exports.tmpName = tmpName;\nmodule.exports.tmpNameSync = tmpNameSync;\n\nmodule.exports.setGracefulCleanup = setGracefulCleanup;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tmp/lib/tmp.js\n");

/***/ })

};
;